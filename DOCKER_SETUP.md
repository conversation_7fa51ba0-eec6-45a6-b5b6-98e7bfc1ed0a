# Docker Setup for Hotel Compare Project

This document explains how to set up PostgreSQL and Redis using Docker for the Hotel Compare project.

## Prerequisites

- Docker installed on your system
- Docker Compose installed

## Services Included

1. **PostgreSQL 15** - Main database
2. **Redis 7** - For Celery task queue
3. **pgAdmin 4** - Database management interface (optional)

## Quick Start

1. **Start all services:**
   ```bash
   docker-compose up -d
   ```

2. **Check service status:**
   ```bash
   docker-compose ps
   ```

3. **View logs:**
   ```bash
   docker-compose logs -f postgres
   docker-compose logs -f redis
   ```

4. **Stop all services:**
   ```bash
   docker-compose down
   ```

## Service Details

### PostgreSQL
- **Port:** 5432
- **Database:** hotelcompare
- **Username:** hotel_user
- **Password:** secure_password
- **Connection URL:** `postgresql://hotel_user:secure_password@localhost:5432/hotelcompare`

### Redis
- **Port:** 6379
- **Connection URL:** `redis://localhost:6379/0`

### pgAdmin (Optional)
- **Port:** 8080
- **URL:** http://localhost:8080
- **Email:** <EMAIL>
- **Password:** admin123

## Django Configuration

Make sure your `.env` file has the correct database settings:

```env
DB_NAME=hotelcompare
DB_USER=hotel_user
DB_PASSWORD=secure_password
DB_HOST=localhost
DB_PORT=5432

CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
```

## Useful Commands

### Database Management
```bash
# Connect to PostgreSQL container
docker exec -it hotelcompare_postgres psql -U hotel_user -d hotelcompare

# Backup database
docker exec hotelcompare_postgres pg_dump -U hotel_user hotelcompare > backup.sql

# Restore database
docker exec -i hotelcompare_postgres psql -U hotel_user -d hotelcompare < backup.sql
```

### Redis Management
```bash
# Connect to Redis container
docker exec -it hotelcompare_redis redis-cli

# Monitor Redis commands
docker exec -it hotelcompare_redis redis-cli monitor
```

### Cleanup
```bash
# Remove containers and volumes (WARNING: This will delete all data)
docker-compose down -v

# Remove only containers (keep data)
docker-compose down
```

## Troubleshooting

1. **Port conflicts:** If ports 5432, 6379, or 8080 are already in use, modify the ports in `docker-compose.yml`

2. **Permission issues:** On Linux, you might need to run Docker commands with `sudo`

3. **Data persistence:** Data is stored in Docker volumes and will persist between container restarts

4. **Connection issues:** Make sure the containers are running and healthy:
   ```bash
   docker-compose ps
   docker-compose logs postgres
   ```

## Production Notes

For production deployment:
1. Change default passwords in `docker-compose.yml`
2. Use environment variables for sensitive data
3. Set up proper backup strategies
4. Configure SSL/TLS for database connections
5. Use Docker secrets for password management
