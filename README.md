# Hotel Compare - Django Web Scraping Project

A comprehensive Django-based hotel comparison platform that scrapes hotel data from Booking.com and Agoda using Scrapy and BeautifulSoup. The project provides a robust web interface for comparing hotel prices and features across multiple platforms.

## 🚀 Features

- **Multi-Platform Scraping**: Automated scraping from Booking.com and Agoda
- **Django Web Interface**: User-friendly web application for hotel search and comparison
- **User Management**: Custom user authentication and profile management
- **Bookmarking System**: Save and organize favorite hotels
- **Advanced Search**: Filter hotels by price, rating, location, and amenities
- **Real-time Data**: Fresh hotel data with automated scraping jobs
- **Admin Dashboard**: Comprehensive admin interface for managing data
- **Asynchronous Processing**: Celery-based task queue for background scraping
- **PostgreSQL Database**: Robust data storage with proper indexing
- **Docker Support**: Easy deployment with Docker containers

## 🛠 Technology Stack

- **Backend**: Django 4.2.7, Python 3.8+
- **Database**: PostgreSQL 15 (with <PERSON><PERSON>)
- **Scraping**: Scrapy, BeautifulSoup4
- **Task Queue**: Celery with Redis
- **Frontend**: Django Templates, Bootstrap (ready for integration)
- **Deployment**: Docker, Docker Compose

## 📋 Prerequisites

- Python 3.8 or higher
- Docker and Docker Compose
- Git

## 🔧 Installation

### 1. Clone the Repository

```bash
git clone <repository-url>
cd sdsmanger-project
```

### 2. Set Up Virtual Environment

```bash
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

### 3. Install Dependencies

```bash
pip install -r requirements.txt
```

### 4. Set Up Environment Variables

```bash
cp .env.example .env
# Edit .env file with your configuration
```

### 5. Set Up PostgreSQL Database

**Option A: Using Docker (Recommended)**
```bash
docker-compose up -d postgres redis
```

**Option B: Using Local PostgreSQL**
```bash
# Start PostgreSQL (macOS with Homebrew)
pg_ctl -D /opt/homebrew/var/postgresql@14 start

# Create database and user
createdb hotelcompare
psql -d hotelcompare -c "CREATE USER hotel_user WITH PASSWORD 'secure_password';"
psql -d hotelcompare -c "GRANT ALL PRIVILEGES ON DATABASE hotelcompare TO hotel_user;"
psql -d hotelcompare -c "ALTER USER hotel_user CREATEDB;"
```

### 6. Run Database Migrations

```bash
python manage.py migrate
```

### 7. Create Superuser

```bash
python manage.py createsuperuser
```

### 8. Start Development Server

```bash
python manage.py runserver
```

The application will be available at `http://127.0.0.1:8000/`

## 🐳 Docker Setup

For detailed Docker setup instructions, see [DOCKER_SETUP.md](DOCKER_SETUP.md).

### Quick Start with Docker

```bash
# Start all services
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f postgres
```

## 📊 Database Configuration

The project uses PostgreSQL as the primary database. Configuration details:

- **Host**: localhost
- **Port**: 5432
- **Database**: hotelcompare
- **Username**: hotel_user
- **Password**: secure_password (change in production)

## 🕷️ Web Scraping

### Running Scrapers

Use the management command to scrape hotel data:

```bash
# Scrape hotels from both platforms for a specific city
python manage.py scrape_hotels --city "New York" --platform both

# Scrape from Booking.com only
python manage.py scrape_hotels --city "Paris" --platform booking

# Scrape from Agoda only
python manage.py scrape_hotels --city "Tokyo" --platform agoda
```

### Scraping Features

- **Rate Limiting**: Respectful scraping with delays
- **Error Handling**: Robust error handling and retry mechanisms
- **Data Validation**: Comprehensive data validation pipelines
- **Duplicate Detection**: Automatic duplicate removal
- **Logging**: Detailed logging for monitoring and debugging

## 🎯 Usage

### Admin Interface

Access the admin interface at `http://127.0.0.1:8000/admin/` to:

- Manage users and permissions
- View and edit hotel data
- Monitor scraping jobs and logs
- Configure system settings

### API Endpoints

- `/hotels/search/` - Hotel search interface
- `/hotels/compare/` - Hotel comparison tool
- `/accounts/` - User authentication
- `/bookmarks/` - Bookmark management
- `/admin/` - Admin interface

## 🔍 Project Structure

```
sdsmanger-project/
├── apps/
│   ├── accounts/          # User management
│   ├── hotels/            # Hotel data models and views
│   ├── bookmarks/         # Bookmarking functionality
│   └── scraper/           # Web scraping components
│       ├── scrapy_project/
│       │   ├── spiders/   # Scrapy spiders
│       │   ├── items.py   # Data items
│       │   ├── pipelines.py # Data processing
│       │   └── settings.py  # Scrapy configuration
│       ├── management/
│       │   └── commands/  # Django management commands
│       ├── models.py      # Scraping job models
│       └── tasks.py       # Celery tasks
├── hotelcompare/          # Django project settings
├── docker-compose.yml     # Docker services
├── requirements.txt       # Python dependencies
└── manage.py             # Django management script
```

## 🧪 Testing

Run the test suite:

```bash
# Run all tests
python manage.py test

# Run specific app tests
python manage.py test apps.hotels

# Run with coverage
coverage run --source='.' manage.py test
coverage report
```

## 📝 Development

### Adding New Scrapers

1. Create a new spider in `apps/scraper/scrapy_project/spiders/`
2. Update the management command to include the new spider
3. Add appropriate data processing in pipelines
4. Test thoroughly with rate limiting

### Database Migrations

```bash
# Create migrations
python manage.py makemigrations

# Apply migrations
python manage.py migrate

# Show migration status
python manage.py showmigrations
```

## 🚀 Deployment

### Production Checklist

- [ ] Update `SECRET_KEY` in production
- [ ] Set `DEBUG = False`
- [ ] Configure proper `ALLOWED_HOSTS`
- [ ] Set up SSL/HTTPS
- [ ] Configure production database
- [ ] Set up proper logging
- [ ] Configure static file serving
- [ ] Set up monitoring and alerts

### Environment Variables

Key environment variables for production:

```env
SECRET_KEY=your-secret-key
DEBUG=False
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
DB_NAME=hotelcompare_prod
DB_USER=prod_user
DB_PASSWORD=secure-production-password
DB_HOST=your-db-host
DB_PORT=5432
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:

- Create an issue in the GitHub repository
- Check the documentation in the `docs/` directory
- Review the Docker setup guide in `DOCKER_SETUP.md`

## ⚙️ Configuration

### Scrapy Settings

The scraping behavior can be configured in `apps/scraper/scrapy_project/settings.py`:

- `DOWNLOAD_DELAY`: Delay between requests (default: 2 seconds)
- `CONCURRENT_REQUESTS`: Number of concurrent requests (default: 1)
- `MAX_PAGES_PER_CITY`: Maximum pages to scrape per city (default: 5)
- `AUTOTHROTTLE_ENABLED`: Enable automatic throttling (default: True)

### Celery Configuration

For background task processing:

```bash
# Start Celery worker
celery -A hotelcompare worker --loglevel=info

# Start Celery beat (for scheduled tasks)
celery -A hotelcompare beat --loglevel=info

# Monitor tasks
celery -A hotelcompare flower
```

## 🔧 Troubleshooting

### Common Issues

1. **Database Connection Error**
   ```bash
   # Check if PostgreSQL is running
   docker-compose ps postgres

   # Restart database
   docker-compose restart postgres
   ```

2. **Scraping Blocked**
   - Increase `DOWNLOAD_DELAY` in Scrapy settings
   - Check if IP is blocked by target websites
   - Use proxy rotation if needed

3. **Migration Issues**
   ```bash
   # Reset migrations (development only)
   python manage.py migrate --fake-initial

   # Check migration status
   python manage.py showmigrations
   ```

4. **Static Files Not Loading**
   ```bash
   # Collect static files
   python manage.py collectstatic
   ```

### Performance Optimization

- Use database indexes for frequently queried fields
- Implement caching for search results
- Optimize Scrapy pipelines for bulk operations
- Use connection pooling for database connections

## 📈 Monitoring

### Logging

Logs are stored in the `logs/` directory:

- `django.log`: Django application logs
- `scrapy.log`: Scraping activity logs
- `celery.log`: Background task logs

### Metrics

Monitor these key metrics:

- Scraping success rate
- Response times
- Database query performance
- Memory and CPU usage
- Error rates

## 🔒 Security

### Best Practices

- Keep dependencies updated
- Use environment variables for sensitive data
- Implement rate limiting
- Validate all user inputs
- Use HTTPS in production
- Regular security audits

### Rate Limiting

The scrapers implement respectful rate limiting:

- 2-second delay between requests
- Random delay variation
- Automatic throttling based on response times
- Respect robots.txt files

## 🔄 Changelog

### Version 1.0.0 (Current)
- Initial release with Booking.com and Agoda scrapers
- Django web interface with user authentication
- Bookmarking and comparison features
- Docker support with PostgreSQL and Redis
- Comprehensive admin interface
- Celery integration for background tasks
- Robust error handling and logging
- Rate limiting and respectful scraping
