# Generated by Django 4.2.7 on 2025-07-10 18:56

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("hotels", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Bookmark",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("hotel_name", models.CharField(max_length=255)),
                ("hotel_city", models.CharField(max_length=100)),
                ("hotel_price", models.DecimalField(decimal_places=2, max_digits=10)),
                ("hotel_platform", models.CharField(max_length=20)),
                ("hotel_url", models.URLField()),
                ("hotel_image_url", models.URLField(blank=True, null=True)),
                (
                    "hotel_star_rating",
                    models.DecimalField(
                        blank=True, decimal_places=1, max_digits=2, null=True
                    ),
                ),
                ("bookmarked_at", models.DateTimeField(auto_now_add=True)),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        help_text="Personal notes about this hotel",
                        null=True,
                    ),
                ),
                (
                    "comparison",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="bookmarks",
                        to="hotels.hotelcomparison",
                    ),
                ),
                (
                    "hotel",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="bookmarks",
                        to="hotels.hotel",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="bookmarks",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Bookmark",
                "verbose_name_plural": "Bookmarks",
                "db_table": "bookmarks_bookmark",
                "ordering": ["-bookmarked_at"],
                "unique_together": {
                    ("user", "hotel_name", "hotel_platform", "hotel_city")
                },
            },
        ),
        migrations.CreateModel(
            name="BookmarkFolder",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("description", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="bookmark_folders",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Bookmark Folder",
                "verbose_name_plural": "Bookmark Folders",
                "db_table": "bookmarks_folder",
                "ordering": ["name"],
                "unique_together": {("user", "name")},
            },
        ),
        migrations.CreateModel(
            name="BookmarkFolderItem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("added_at", models.DateTimeField(auto_now_add=True)),
                (
                    "bookmark",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="folder_items",
                        to="bookmarks.bookmark",
                    ),
                ),
                (
                    "folder",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="bookmarks",
                        to="bookmarks.bookmarkfolder",
                    ),
                ),
            ],
            options={
                "verbose_name": "Bookmark Folder Item",
                "verbose_name_plural": "Bookmark Folder Items",
                "db_table": "bookmarks_folder_item",
                "unique_together": {("bookmark", "folder")},
            },
        ),
    ]
