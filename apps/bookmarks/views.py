"""
Views for the bookmarks app.
"""
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.core.paginator import Paginator
from django.db import IntegrityError
from .models import Bookmark, BookmarkFolder, BookmarkFolderItem
from apps.hotels.models import Hotel, HotelComparison
import json


@login_required
def bookmark_list(request):
    """
    Display user's bookmarks.
    """
    bookmarks = Bookmark.objects.filter(user=request.user).order_by('-created_at')

    context = {
        'bookmarks': bookmarks,
    }

    return render(request, 'bookmarks/list.html', context)


@login_required
@require_http_methods(["POST"])
def toggle_bookmark(request):
    """
    Toggle bookmark status for a hotel via AJAX.
    """
    try:
        hotel_id = request.POST.get('hotel_id')

        if not hotel_id:
            return JsonResponse({'success': False, 'error': 'Hotel ID is required'})

        try:
            hotel = Hotel.objects.get(id=hotel_id)
        except Hotel.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'Hotel not found'})

        # Check if bookmark exists
        bookmark = Bookmark.objects.filter(user=request.user, hotel=hotel).first()

        if bookmark:
            # Remove bookmark
            bookmark.delete()
            return JsonResponse({
                'success': True,
                'bookmarked': False,
                'message': f'{hotel.name} removed from bookmarks'
            })
        else:
            # Add bookmark
            bookmark = Bookmark.objects.create(
                user=request.user,
                hotel=hotel
            )
            return JsonResponse({
                'success': True,
                'bookmarked': True,
                'message': f'{hotel.name} added to bookmarks'
            })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
@require_http_methods(["POST"])
def add_bookmark(request):
    """
    Add a hotel to bookmarks via AJAX.
    """
    try:
        data = json.loads(request.body)
        hotel_id = data.get('hotel_id')
        comparison_id = data.get('comparison_id')
        notes = data.get('notes', '')
        
        bookmark_data = {
            'user': request.user,
            'notes': notes
        }
        
        if hotel_id:
            try:
                hotel = Hotel.objects.get(id=hotel_id)
                bookmark_data['hotel'] = hotel
            except Hotel.DoesNotExist:
                return JsonResponse({'success': False, 'error': 'Hotel not found'})
        
        elif comparison_id:
            try:
                comparison = HotelComparison.objects.get(id=comparison_id)
                bookmark_data['comparison'] = comparison
            except HotelComparison.DoesNotExist:
                return JsonResponse({'success': False, 'error': 'Comparison not found'})
        
        else:
            return JsonResponse({'success': False, 'error': 'No hotel or comparison specified'})
        
        # Create bookmark
        bookmark = Bookmark.objects.create(**bookmark_data)
        
        return JsonResponse({
            'success': True,
            'message': f'{bookmark.hotel_name} has been bookmarked!',
            'bookmark_id': bookmark.id
        })
    
    except IntegrityError:
        return JsonResponse({
            'success': False,
            'error': 'This hotel is already in your bookmarks'
        })
    
    except json.JSONDecodeError:
        return JsonResponse({'success': False, 'error': 'Invalid JSON data'})
    
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
@require_http_methods(["POST"])
def remove_bookmark(request, bookmark_id):
    """
    Remove a bookmark.
    """
    try:
        bookmark = get_object_or_404(Bookmark, id=bookmark_id, user=request.user)
        hotel_name = bookmark.hotel_name
        bookmark.delete()
        
        if request.headers.get('Content-Type') == 'application/json':
            return JsonResponse({
                'success': True,
                'message': f'{hotel_name} has been removed from bookmarks'
            })
        else:
            messages.success(request, f'{hotel_name} has been removed from bookmarks.')
            return redirect('bookmarks:list')
    
    except Exception as e:
        if request.headers.get('Content-Type') == 'application/json':
            return JsonResponse({'success': False, 'error': str(e)})
        else:
            messages.error(request, 'Error removing bookmark.')
            return redirect('bookmarks:list')


@login_required
def bookmark_detail(request, bookmark_id):
    """
    Display detailed view of a bookmark.
    """
    bookmark = get_object_or_404(Bookmark, id=bookmark_id, user=request.user)
    
    # Get folders this bookmark belongs to
    folder_items = BookmarkFolderItem.objects.filter(bookmark=bookmark)
    folders = [item.folder for item in folder_items]
    
    context = {
        'bookmark': bookmark,
        'folders': folders,
    }
    
    return render(request, 'bookmarks/detail.html', context)


@login_required
def folder_list(request):
    """
    Display user's bookmark folders.
    """
    folders = BookmarkFolder.objects.filter(user=request.user)
    
    context = {
        'folders': folders,
    }
    
    return render(request, 'bookmarks/folders.html', context)


@login_required
@require_http_methods(["POST"])
def create_folder(request):
    """
    Create a new bookmark folder.
    """
    try:
        if request.content_type == 'application/json':
            data = json.loads(request.body)
            name = data.get('name', '').strip()
            description = data.get('description', '').strip()
        else:
            name = request.POST.get('name', '').strip()
            description = request.POST.get('description', '').strip()
        
        if not name:
            error_msg = 'Folder name is required'
            if request.content_type == 'application/json':
                return JsonResponse({'success': False, 'error': error_msg})
            else:
                messages.error(request, error_msg)
                return redirect('bookmarks:folders')
        
        folder = BookmarkFolder.objects.create(
            user=request.user,
            name=name,
            description=description
        )
        
        success_msg = f'Folder "{folder.name}" created successfully'
        if request.content_type == 'application/json':
            return JsonResponse({
                'success': True,
                'message': success_msg,
                'folder_id': folder.id
            })
        else:
            messages.success(request, success_msg)
            return redirect('bookmarks:folders')
    
    except IntegrityError:
        error_msg = 'A folder with this name already exists'
        if request.content_type == 'application/json':
            return JsonResponse({'success': False, 'error': error_msg})
        else:
            messages.error(request, error_msg)
            return redirect('bookmarks:folders')
    
    except Exception as e:
        if request.content_type == 'application/json':
            return JsonResponse({'success': False, 'error': str(e)})
        else:
            messages.error(request, 'Error creating folder.')
            return redirect('bookmarks:folders')


@login_required
@require_http_methods(["POST"])
def add_to_folder(request):
    """
    Add a bookmark to a folder.
    """
    try:
        data = json.loads(request.body)
        bookmark_id = data.get('bookmark_id')
        folder_id = data.get('folder_id')
        
        bookmark = get_object_or_404(Bookmark, id=bookmark_id, user=request.user)
        folder = get_object_or_404(BookmarkFolder, id=folder_id, user=request.user)
        
        folder_item, created = BookmarkFolderItem.objects.get_or_create(
            bookmark=bookmark,
            folder=folder
        )
        
        if created:
            return JsonResponse({
                'success': True,
                'message': f'{bookmark.hotel_name} added to {folder.name}'
            })
        else:
            return JsonResponse({
                'success': False,
                'error': 'Bookmark is already in this folder'
            })
    
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
@require_http_methods(["POST"])
def remove_from_folder(request, bookmark_id, folder_id):
    """
    Remove a bookmark from a folder.
    """
    try:
        bookmark = get_object_or_404(Bookmark, id=bookmark_id, user=request.user)
        folder = get_object_or_404(BookmarkFolder, id=folder_id, user=request.user)
        
        folder_item = get_object_or_404(
            BookmarkFolderItem,
            bookmark=bookmark,
            folder=folder
        )
        
        folder_item.delete()
        
        return JsonResponse({
            'success': True,
            'message': f'{bookmark.hotel_name} removed from {folder.name}'
        })
    
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})
