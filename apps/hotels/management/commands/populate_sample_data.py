"""
Django management command to populate sample hotel data for testing.
"""
from django.core.management.base import BaseCommand
from django.utils import timezone
from apps.hotels.models import Hotel
import random


class Command(BaseCommand):
    help = 'Populate database with sample hotel data for testing'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=50,
            help='Number of sample hotels to create'
        )
        parser.add_argument(
            '--city',
            type=str,
            default='New York',
            help='City for sample hotels'
        )
    
    def handle(self, *args, **options):
        count = options['count']
        city = options['city']
        
        self.stdout.write(f"Creating {count} sample hotels for {city}...")
        
        # Sample hotel names
        hotel_names = [
            "Grand Plaza Hotel", "Royal Palace Inn", "City Center Lodge", "Luxury Suites",
            "Business Hotel", "Comfort Inn", "Boutique Hotel", "Garden View Resort",
            "Downtown Hotel", "Riverside Inn", "Mountain View Lodge", "Seaside Resort",
            "Executive Suites", "Budget Inn", "Premium Hotel", "Historic Hotel",
            "Modern Suites", "Family Hotel", "Spa Resort", "Conference Center",
            "Airport Hotel", "Metro Inn", "Central Plaza", "Park View Hotel",
            "Skyline Suites", "Harbor Hotel", "Urban Lodge", "Classic Inn",
            "Deluxe Hotel", "Express Inn", "Continental Hotel", "Ambassador Suites",
            "Crown Plaza", "Golden Gate Hotel", "Silver Star Inn", "Diamond Resort",
            "Emerald Hotel", "Ruby Suites", "Sapphire Inn", "Pearl Hotel",
            "Crystal Palace", "Platinum Suites", "Elite Hotel", "Premier Inn",
            "Supreme Lodge", "Imperial Hotel", "Majestic Suites", "Regal Inn",
            "Noble Hotel", "Prestige Suites", "Excellence Hotel", "Grandeur Inn"
        ]
        
        # Sample amenities
        amenities_list = [
            ["Free WiFi", "Pool", "Gym"],
            ["Free WiFi", "Restaurant", "Bar"],
            ["Pool", "Spa", "Room Service"],
            ["Free WiFi", "Parking", "Business Center"],
            ["Restaurant", "Bar", "Concierge"],
            ["Pool", "Gym", "Free Breakfast"],
            ["Spa", "Room Service", "Laundry"],
            ["Free WiFi", "Pool", "Restaurant", "Gym"],
            ["Parking", "Business Center", "Meeting Rooms"],
            ["Bar", "Concierge", "Airport Shuttle"],
        ]
        
        # Sample descriptions
        descriptions = [
            "A luxurious hotel in the heart of the city with modern amenities and excellent service.",
            "Comfortable accommodations perfect for business travelers and tourists alike.",
            "Elegant hotel featuring spacious rooms and world-class dining options.",
            "Budget-friendly hotel with clean rooms and friendly staff.",
            "Boutique hotel with unique design and personalized service.",
            "Family-friendly hotel with recreational facilities and convenient location.",
            "Modern hotel with state-of-the-art facilities and stunning city views.",
            "Historic hotel combining classic charm with contemporary comfort.",
            "Eco-friendly hotel committed to sustainability and guest satisfaction.",
            "Premium hotel offering exceptional luxury and unparalleled service.",
        ]
        
        created_count = 0
        
        for i in range(count):
            # Randomly select hotel details
            name = random.choice(hotel_names)
            platform = random.choice(['booking', 'agoda'])
            price = random.randint(50, 500)
            star_rating = random.choice([3, 3, 4, 4, 4, 5])  # Weighted towards 4-5 stars
            amenities = random.choice(amenities_list)
            description = random.choice(descriptions)
            
            # Create unique hotel name by adding suffix if needed
            base_name = name
            suffix = 1
            while Hotel.objects.filter(name=name, platform=platform, city=city).exists():
                name = f"{base_name} {suffix}"
                suffix += 1
            
            # Create hotel
            hotel = Hotel.objects.create(
                name=name,
                image_url=f"https://via.placeholder.com/400x300?text={name.replace(' ', '+')}",
                star_rating=star_rating,
                platform=platform,
                price=price,
                currency='USD',
                url=f"https://{platform}.com/hotel/{name.lower().replace(' ', '-')}",
                city=city,
                address=f"{random.randint(100, 9999)} {random.choice(['Main St', 'Broadway', 'Park Ave', 'First Ave', 'Central St'])}",
                description=description,
                amenities=amenities,
                crawl_timestamp=timezone.now()
            )
            
            created_count += 1
            
            if created_count % 10 == 0:
                self.stdout.write(f"Created {created_count} hotels...")
        
        self.stdout.write(
            self.style.SUCCESS(f"Successfully created {created_count} sample hotels for {city}")
        )
