"""
Management command to validate and cleanup hotel data
"""
from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone
from datetime import timedelta
from apps.hotels.models import Hotel
from apps.hotels.services.hotel_validator import HotelValidator
from apps.hotels.services.agoda_url_generator import AgodaUrlGenerator


class Command(BaseCommand):
    help = 'Validate hotel data and remove outdated entries'

    def add_arguments(self, parser):
        parser.add_argument(
            '--city',
            type=str,
            help='Validate hotels for specific city only'
        )
        parser.add_argument(
            '--max-age-hours',
            type=int,
            default=24,
            help='Maximum age of hotel data in hours before validation (default: 24)'
        )
        parser.add_argument(
            '--cleanup-old',
            action='store_true',
            help='Remove hotels older than 7 days'
        )
        parser.add_argument(
            '--update-agoda-urls',
            action='store_true',
            help='Update Agoda URLs to proper hotel links'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes'
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🔍 Starting hotel validation and cleanup')
        )

        validator = HotelValidator()
        
        # Get validation statistics
        if options['city']:
            stats = validator.get_validation_stats(options['city'])
            self.stdout.write(f"📊 Stats for {options['city']}: {stats}")
        else:
            stats = validator.get_validation_stats()
            self.stdout.write(f"📊 Overall stats: {stats}")

        # Validate hotels
        if options['city']:
            cities = [options['city']]
        else:
            # Get all cities with hotels
            cities = Hotel.objects.values_list('city', flat=True).distinct()

        total_results = {
            'validated': 0,
            'removed': 0,
            'updated': 0,
            'errors': 0
        }

        for city in cities:
            self.stdout.write(f"\n🏙️  Processing {city}...")
            
            if not options['dry_run']:
                try:
                    with transaction.atomic():
                        results = validator.validate_and_cleanup_hotels(
                            city, 
                            max_age_hours=options['max_age_hours']
                        )
                        
                        for key in total_results:
                            total_results[key] += results[key]
                        
                        self.stdout.write(
                            f"   ✅ {city}: {results['validated']} validated, "
                            f"{results['removed']} removed, {results['errors']} errors"
                        )
                        
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f"   ❌ Error processing {city}: {e}")
                    )
            else:
                # Dry run - just show what would be validated
                hotels_to_validate = Hotel.objects.filter(
                    city__iexact=city,
                    crawl_timestamp__lt=timezone.now() - timedelta(hours=options['max_age_hours'])
                ).count()
                
                self.stdout.write(f"   📋 Would validate {hotels_to_validate} hotels")

        # Update Agoda URLs if requested
        if options['update_agoda_urls'] and not options['dry_run']:
            self.stdout.write("\n🅰️  Updating Agoda URLs...")
            agoda_generator = AgodaUrlGenerator()
            
            for city in cities:
                try:
                    agoda_stats = agoda_generator.update_hotel_urls(city, limit=50)
                    self.stdout.write(
                        f"   ✅ {city}: {agoda_stats['updated']} URLs updated, "
                        f"{agoda_stats['failed']} failed"
                    )
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f"   ❌ Error updating Agoda URLs for {city}: {e}")
                    )

        # Cleanup old hotels if requested
        if options['cleanup_old'] and not options['dry_run']:
            self.stdout.write("\n🧹 Cleaning up old hotels...")
            removed_count = validator.cleanup_old_hotels(days_old=7)
            self.stdout.write(f"   ✅ Removed {removed_count} old hotels")

        # Final summary
        self.stdout.write(f"\n📊 FINAL SUMMARY:")
        self.stdout.write(f"   ✅ Validated: {total_results['validated']}")
        self.stdout.write(f"   🗑️  Removed: {total_results['removed']}")
        self.stdout.write(f"   🔄 Updated: {total_results['updated']}")
        self.stdout.write(f"   ❌ Errors: {total_results['errors']}")

        if options['dry_run']:
            self.stdout.write(
                self.style.WARNING("\n⚠️  This was a dry run - no changes were made")
            )
        else:
            self.stdout.write(
                self.style.SUCCESS("\n🎉 Hotel validation and cleanup complete!")
            )
