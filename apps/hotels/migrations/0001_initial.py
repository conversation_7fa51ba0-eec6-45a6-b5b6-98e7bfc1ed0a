# Generated by Django 4.2.7 on 2025-07-10 18:56

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Hotel",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.Char<PERSON>ield(max_length=255)),
                ("image_url", models.URLField(blank=True, null=True)),
                (
                    "star_rating",
                    models.DecimalField(
                        blank=True,
                        decimal_places=1,
                        max_digits=2,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0.0),
                            django.core.validators.MaxValueValidator(5.0),
                        ],
                    ),
                ),
                (
                    "platform",
                    models.Char<PERSON>ield(
                        choices=[("booking", "Booking.com"), ("agoda", "Agoda")],
                        max_length=20,
                    ),
                ),
                ("price", models.DecimalField(decimal_places=2, max_digits=10)),
                ("currency", models.CharField(default="USD", max_length=3)),
                ("url", models.URLField()),
                ("city", models.CharField(max_length=100)),
                ("address", models.TextField(blank=True, null=True)),
                ("description", models.TextField(blank=True, null=True)),
                ("amenities", models.JSONField(blank=True, default=list)),
                ("crawl_timestamp", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "verbose_name": "Hotel",
                "verbose_name_plural": "Hotels",
                "db_table": "hotels_hotel",
            },
        ),
        migrations.CreateModel(
            name="SearchQuery",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("city", models.CharField(max_length=100)),
                (
                    "min_price",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                (
                    "max_price",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                (
                    "star_rating",
                    models.IntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                    ),
                ),
                ("requested_at", models.DateTimeField(auto_now_add=True)),
                ("results_count", models.IntegerField(default=0)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="search_queries",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Search Query",
                "verbose_name_plural": "Search Queries",
                "db_table": "hotels_search_query",
                "ordering": ["-requested_at"],
            },
        ),
        migrations.CreateModel(
            name="HotelComparison",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("hotel_name", models.CharField(max_length=255)),
                ("city", models.CharField(max_length=100)),
                ("best_price", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "best_platform",
                    models.CharField(
                        choices=[("booking", "Booking.com"), ("agoda", "Agoda")],
                        max_length=20,
                    ),
                ),
                (
                    "price_difference",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "agoda_hotel",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="agoda_comparisons",
                        to="hotels.hotel",
                    ),
                ),
                (
                    "booking_hotel",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="booking_comparisons",
                        to="hotels.hotel",
                    ),
                ),
                (
                    "search_query",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="comparisons",
                        to="hotels.searchquery",
                    ),
                ),
            ],
            options={
                "verbose_name": "Hotel Comparison",
                "verbose_name_plural": "Hotel Comparisons",
                "db_table": "hotels_comparison",
                "ordering": ["best_price"],
            },
        ),
        migrations.AddIndex(
            model_name="hotel",
            index=models.Index(
                fields=["city", "platform"], name="hotels_hote_city_7d296f_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="hotel",
            index=models.Index(fields=["price"], name="hotels_hote_price_153fed_idx"),
        ),
        migrations.AddIndex(
            model_name="hotel",
            index=models.Index(
                fields=["star_rating"], name="hotels_hote_star_ra_e9d0d2_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="hotel",
            index=models.Index(
                fields=["crawl_timestamp"], name="hotels_hote_crawl_t_08fd13_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="hotel",
            unique_together={("name", "platform", "city", "url")},
        ),
    ]
