"""
Agoda URL generation service for better hotel links
"""
import requests
import logging
from urllib.parse import quote
from bs4 import BeautifulSoup
import re
import time
import random

logger = logging.getLogger(__name__)


class AgodaUrlGenerator:
    """Service to generate proper Agoda hotel URLs"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    def get_hotel_url(self, hotel_name, city):
        """
        Get a proper Agoda URL for a specific hotel

        Args:
            hotel_name: Name of the hotel
            city: City where the hotel is located

        Returns:
            str: Agoda URL for the hotel
        """
        try:
            # Create hotel-specific URL directly
            return self._create_hotel_specific_url(hotel_name, city)

        except Exception as e:
            logger.error(f"Error generating Agoda URL for {hotel_name}: {e}")
            return self._create_hotel_specific_url(hotel_name, city)

    def _create_hotel_specific_url(self, hotel_name, city):
        """Create a hotel-specific Agoda URL"""
        import re

        # Clean and format hotel name for URL
        clean_name = hotel_name.lower()
        clean_name = re.sub(r'[^a-z0-9\s]', '', clean_name)  # Remove special chars
        clean_name = re.sub(r'\s+', '-', clean_name.strip())  # Replace spaces with hyphens

        # Clean and format city name
        clean_city = city.lower()
        clean_city = re.sub(r'[^a-z0-9\s]', '', clean_city)
        clean_city = re.sub(r'\s+', '-', clean_city.strip())

        # Create Agoda-style URL
        agoda_url = f"https://www.agoda.com/{clean_name}/hotel/{clean_city}.html"
        agoda_url += "?checkIn=2024-12-15&checkOut=2024-12-16&rooms=1&adults=2"

        return agoda_url

    def _build_search_url(self, city):
        """Build basic Agoda search URL for city"""
        encoded_city = quote(city)
        return f"https://www.agoda.com/search?city={encoded_city}&checkIn=2024-12-15&checkOut=2024-12-16&rooms=1&adults=2"
    
    def _build_search_url_with_hotel(self, hotel_name, city):
        """Build Agoda search URL with hotel name"""
        encoded_city = quote(city)
        encoded_hotel = quote(hotel_name)
        return f"https://www.agoda.com/search?city={encoded_city}&searchText={encoded_hotel}&checkIn=2024-12-15&checkOut=2024-12-16&rooms=1&adults=2"
    
    def _find_hotel_in_search(self, search_url, hotel_name):
        """
        Search for specific hotel in Agoda search results
        
        Args:
            search_url: Agoda search URL
            hotel_name: Name of hotel to find
            
        Returns:
            str: Hotel-specific URL if found, None otherwise
        """
        try:
            response = self.session.get(search_url, timeout=15)
            if response.status_code != 200:
                return None
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Look for hotel links in search results
            hotel_links = self._extract_hotel_links(soup)
            
            # Find matching hotel by name
            hotel_name_lower = hotel_name.lower()
            hotel_name_parts = hotel_name_lower.split()
            
            for link_data in hotel_links:
                link_name = link_data['name'].lower()
                
                # Check for exact match first
                if hotel_name_lower == link_name:
                    return link_data['url']
                
                # Check for partial match (at least 2 significant words)
                significant_parts = [part for part in hotel_name_parts if len(part) > 3]
                if len(significant_parts) >= 2:
                    matches = sum(1 for part in significant_parts if part in link_name)
                    if matches >= 2:
                        return link_data['url']
            
            return None
            
        except Exception as e:
            logger.error(f"Error finding hotel in search: {e}")
            return None
    
    def _extract_hotel_links(self, soup):
        """Extract hotel links from Agoda search results"""
        hotel_links = []
        
        try:
            # Multiple selectors for different Agoda page layouts
            selectors = [
                'a[data-selenium="hotel-item"]',
                'a[href*="/hotel/"]',
                'a[href*="agoda.com"][href*="hotel"]',
                '.PropertyCard a',
                '.hotel-item a',
                'a[href*="/property/"]'
            ]
            
            for selector in selectors:
                links = soup.select(selector)
                
                for link in links:
                    href = link.get('href')
                    if not href:
                        continue
                    
                    # Make absolute URL
                    if href.startswith('/'):
                        href = f"https://www.agoda.com{href}"
                    
                    # Extract hotel name
                    name = self._extract_hotel_name_from_link(link)
                    
                    if name and href:
                        hotel_links.append({
                            'name': name,
                            'url': href
                        })
                
                if hotel_links:  # If we found links with this selector, use them
                    break
            
            return hotel_links[:20]  # Limit to first 20 results
            
        except Exception as e:
            logger.error(f"Error extracting hotel links: {e}")
            return []
    
    def _extract_hotel_name_from_link(self, link):
        """Extract hotel name from a link element"""
        try:
            # Try different methods to get hotel name
            
            # Method 1: data attributes
            name = link.get('data-hotel-name') or link.get('data-name')
            if name:
                return name.strip()
            
            # Method 2: text content
            name = link.get_text(strip=True)
            if name and len(name) > 3:
                return name
            
            # Method 3: title attribute
            name = link.get('title')
            if name:
                return name.strip()
            
            # Method 4: look for hotel name in child elements
            name_elem = link.find(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
            if name_elem:
                name = name_elem.get_text(strip=True)
                if name:
                    return name
            
            # Method 5: look for specific classes
            name_elem = link.find(class_=re.compile(r'hotel.*name|name.*hotel|property.*name', re.I))
            if name_elem:
                name = name_elem.get_text(strip=True)
                if name:
                    return name
            
            return None
            
        except Exception:
            return None
    
    def _try_alternative_search(self, hotel_name, city):
        """Try alternative search methods for hotel"""
        try:
            # Try search with hotel name as main search term
            encoded_hotel = quote(f"{hotel_name} {city}")
            alt_url = f"https://www.agoda.com/search?searchText={encoded_hotel}&checkIn=2024-12-15&checkOut=2024-12-16&rooms=1&adults=2"
            
            hotel_url = self._find_hotel_in_search(alt_url, hotel_name)
            if hotel_url:
                return hotel_url
            
            # Try with simplified hotel name
            simplified_name = self._simplify_hotel_name(hotel_name)
            if simplified_name != hotel_name:
                encoded_simple = quote(f"{simplified_name} {city}")
                simple_url = f"https://www.agoda.com/search?searchText={encoded_simple}&checkIn=2024-12-15&checkOut=2024-12-16&rooms=1&adults=2"
                
                hotel_url = self._find_hotel_in_search(simple_url, hotel_name)
                if hotel_url:
                    return hotel_url
            
            return None
            
        except Exception as e:
            logger.error(f"Error in alternative search: {e}")
            return None
    
    def _simplify_hotel_name(self, hotel_name):
        """Simplify hotel name for better search results"""
        # Remove common words that might interfere with search
        words_to_remove = [
            'hotel', 'inn', 'resort', 'suites', 'lodge', 'motel',
            'the', 'a', 'an', 'and', 'or', 'of', 'in', 'at', 'by'
        ]
        
        words = hotel_name.lower().split()
        simplified_words = [word for word in words if word not in words_to_remove]
        
        return ' '.join(simplified_words) if simplified_words else hotel_name
    
    def update_hotel_urls(self, city, limit=50):
        """
        Update Agoda hotel URLs for a specific city
        
        Args:
            city: City to update hotels for
            limit: Maximum number of hotels to update
            
        Returns:
            dict: Update statistics
        """
        from apps.hotels.models import Hotel
        
        agoda_hotels = Hotel.objects.filter(
            platform='agoda',
            city__iexact=city
        )[:limit]
        
        stats = {
            'total': agoda_hotels.count(),
            'updated': 0,
            'failed': 0
        }
        
        logger.info(f"🔄 Updating Agoda URLs for {stats['total']} hotels in {city}")
        
        for hotel in agoda_hotels:
            try:
                new_url = self.get_hotel_url(hotel.name, hotel.city)
                
                if new_url != hotel.url:
                    hotel.url = new_url
                    hotel.save()
                    stats['updated'] += 1
                    logger.info(f"✅ Updated URL for {hotel.name}")
                
                # Rate limiting
                time.sleep(random.uniform(0.5, 1.5))
                
            except Exception as e:
                stats['failed'] += 1
                logger.error(f"❌ Failed to update {hotel.name}: {e}")
        
        logger.info(f"🎯 URL update complete: {stats}")
        return stats
