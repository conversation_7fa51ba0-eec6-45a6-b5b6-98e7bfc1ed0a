"""
Hotel validation and cleanup service
"""
import requests
import logging
from datetime import datetime, timedelta
from django.utils import timezone
from django.db import transaction
from apps.hotels.models import Hotel
from bs4 import BeautifulSoup
import time
import random

logger = logging.getLogger(__name__)


class HotelValidator:
    """Service to validate and cleanup hotel data"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    def validate_and_cleanup_hotels(self, city, max_age_hours=24):
        """
        Validate hotels for a city and remove outdated/invalid ones
        
        Args:
            city: City name to validate hotels for
            max_age_hours: Maximum age of hotel data in hours before validation
        """
        logger.info(f"🔍 Starting hotel validation for {city}")
        
        # Get hotels that need validation (old or never validated)
        cutoff_time = timezone.now() - timedelta(hours=max_age_hours)
        hotels_to_validate = Hotel.objects.filter(
            city__iexact=city,
            crawl_timestamp__lt=cutoff_time
        )
        
        logger.info(f"📊 Found {hotels_to_validate.count()} hotels to validate for {city}")
        
        validation_results = {
            'validated': 0,
            'removed': 0,
            'updated': 0,
            'errors': 0
        }
        
        for hotel in hotels_to_validate:
            try:
                is_valid = self._validate_hotel(hotel)
                
                if is_valid:
                    # Update timestamp to mark as recently validated
                    hotel.crawl_timestamp = timezone.now()
                    hotel.save()
                    validation_results['validated'] += 1
                    logger.info(f"✅ Validated: {hotel.name} ({hotel.platform})")
                else:
                    # Remove invalid hotel
                    hotel_name = hotel.name
                    platform = hotel.platform
                    hotel.delete()
                    validation_results['removed'] += 1
                    logger.info(f"🗑️  Removed: {hotel_name} ({platform}) - No longer available")
                
                # Rate limiting
                time.sleep(random.uniform(1, 2))
                
            except Exception as e:
                validation_results['errors'] += 1
                logger.error(f"❌ Error validating {hotel.name}: {e}")
                continue
        
        logger.info(f"🎯 Validation complete for {city}: {validation_results}")
        return validation_results
    
    def _validate_hotel(self, hotel):
        """
        Validate a single hotel by checking its URL
        
        Args:
            hotel: Hotel instance to validate
            
        Returns:
            bool: True if hotel is still valid, False otherwise
        """
        try:
            if hotel.platform == 'booking':
                return self._validate_booking_hotel(hotel)
            elif hotel.platform == 'agoda':
                return self._validate_agoda_hotel(hotel)
            else:
                logger.warning(f"Unknown platform: {hotel.platform}")
                return False
                
        except Exception as e:
            logger.error(f"Error validating hotel {hotel.name}: {e}")
            return False
    
    def _validate_booking_hotel(self, hotel):
        """Validate a Booking.com hotel"""
        try:
            response = self.session.get(hotel.url, timeout=10, allow_redirects=True)
            
            # Check if page exists and is not an error page
            if response.status_code == 404:
                return False
            
            if response.status_code != 200:
                # If we can't access it, assume it's still valid (might be temporary)
                return True
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Check for common Booking.com error indicators
            error_indicators = [
                'property is no longer available',
                'property not found',
                'page not found',
                'error 404',
                'property has been removed'
            ]
            
            page_text = soup.get_text().lower()
            for indicator in error_indicators:
                if indicator in page_text:
                    return False
            
            # Check if hotel name is still present (basic validation)
            hotel_name_parts = hotel.name.lower().split()
            if len(hotel_name_parts) > 1:
                # Check if at least part of the hotel name is present
                name_found = any(part in page_text for part in hotel_name_parts if len(part) > 3)
                if not name_found:
                    return False
            
            return True
            
        except requests.exceptions.RequestException:
            # Network errors - assume hotel is still valid
            return True
        except Exception as e:
            logger.error(f"Error validating Booking.com hotel: {e}")
            return True  # Conservative approach
    
    def _validate_agoda_hotel(self, hotel):
        """Validate an Agoda hotel"""
        try:
            # For Agoda, we'll use a different approach since we're using search URLs
            # We'll validate by checking if the hotel appears in search results
            
            # Extract city from the search URL or use hotel.city
            city = hotel.city
            
            # Perform a search and see if this hotel appears
            search_url = f"https://www.agoda.com/search?city={city}&checkIn=2024-12-15&checkOut=2024-12-16&rooms=1&adults=2"
            
            response = self.session.get(search_url, timeout=15, allow_redirects=True)
            
            if response.status_code != 200:
                # If search fails, assume hotel is still valid
                return True
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Look for hotel name in search results
            page_text = soup.get_text().lower()
            hotel_name_lower = hotel.name.lower()
            
            # Check if hotel name appears in the search results
            hotel_name_parts = hotel_name_lower.split()
            if len(hotel_name_parts) > 1:
                # Check if significant parts of the hotel name are present
                significant_parts = [part for part in hotel_name_parts if len(part) > 3]
                if significant_parts:
                    name_found = any(part in page_text for part in significant_parts)
                    return name_found
            else:
                return hotel_name_lower in page_text
            
            return True  # Conservative approach
            
        except requests.exceptions.RequestException:
            # Network errors - assume hotel is still valid
            return True
        except Exception as e:
            logger.error(f"Error validating Agoda hotel: {e}")
            return True  # Conservative approach
    
    def cleanup_old_hotels(self, days_old=7):
        """
        Remove hotels older than specified days
        
        Args:
            days_old: Number of days after which to remove hotels
        """
        cutoff_date = timezone.now() - timedelta(days=days_old)
        old_hotels = Hotel.objects.filter(crawl_timestamp__lt=cutoff_date)
        
        count = old_hotels.count()
        if count > 0:
            logger.info(f"🧹 Removing {count} hotels older than {days_old} days")
            old_hotels.delete()
            return count
        
        return 0
    
    def get_validation_stats(self, city=None):
        """Get validation statistics"""
        queryset = Hotel.objects.all()
        if city:
            queryset = queryset.filter(city__iexact=city)
        
        now = timezone.now()
        recent_cutoff = now - timedelta(hours=24)
        old_cutoff = now - timedelta(days=7)
        
        stats = {
            'total_hotels': queryset.count(),
            'recent_hotels': queryset.filter(crawl_timestamp__gte=recent_cutoff).count(),
            'old_hotels': queryset.filter(crawl_timestamp__lt=old_cutoff).count(),
            'booking_hotels': queryset.filter(platform='booking').count(),
            'agoda_hotels': queryset.filter(platform='agoda').count(),
        }
        
        return stats


def validate_hotels_for_search(city):
    """
    Convenience function to validate hotels before search
    
    Args:
        city: City name to validate hotels for
        
    Returns:
        dict: Validation results
    """
    validator = HotelValidator()
    return validator.validate_and_cleanup_hotels(city, max_age_hours=24)
