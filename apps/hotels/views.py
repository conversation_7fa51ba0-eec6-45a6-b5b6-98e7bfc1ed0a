"""
Views for the hotels app.
"""
import json
from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.core.paginator import Paginator
from django.db.models import Q, Min, Max
from django.utils import timezone
from datetime import timedelta
from .forms import HotelSearchForm
from .models import Hotel, SearchQuery, HotelComparison
from apps.scraper.tasks import scrape_hotels_task


@login_required
def search_view(request):
    """
    Hotel search view - main search page with search functionality.
    """
    hotels = []
    city = None

    if request.method == 'POST':
        # Get search parameters
        city = request.POST.get('city', '').strip()
        min_price = request.POST.get('min_price')
        max_price = request.POST.get('max_price')
        star_rating = request.POST.get('star_rating')

        if city:
            # Create search query record
            search_query = SearchQuery.objects.create(
                user=request.user,
                city=city,
                min_price=int(min_price) if min_price else None,
                max_price=int(max_price) if max_price else None,
                star_rating=int(star_rating) if star_rating else None
            )

            # Check for recent results (within last 30 minutes)
            recent_cutoff = timezone.now() - timedelta(minutes=30)
            hotels_queryset = Hotel.objects.filter(
                city__iexact=city,
                crawl_timestamp__gte=recent_cutoff
            )

            # Apply filters
            if min_price:
                hotels_queryset = hotels_queryset.filter(price__gte=int(min_price))
            if max_price:
                hotels_queryset = hotels_queryset.filter(price__lte=int(max_price))
            if star_rating:
                hotels_queryset = hotels_queryset.filter(star_rating__gte=int(star_rating))

            all_hotels = list(hotels_queryset.order_by('price'))

            if not all_hotels:
                # Trigger real-time scraping
                messages.info(request, f'No recent data found for {city}. In a real implementation, scraping would start now.')
            else:
                # Group hotels by name for comparison
                hotel_comparisons = create_hotel_comparisons(all_hotels)

                # Add bookmark status for each hotel comparison
                from apps.bookmarks.models import Bookmark
                bookmarked_hotel_ids = set(
                    Bookmark.objects.filter(
                        user=request.user,
                        hotel__in=all_hotels
                    ).values_list('hotel_id', flat=True)
                )

                # Add bookmark status to comparisons
                for comparison in hotel_comparisons:
                    comparison['is_bookmarked'] = any(
                        hotel.id in bookmarked_hotel_ids
                        for hotel in [comparison.get('booking_hotel'), comparison.get('agoda_hotel')]
                        if hotel
                    )

                # Limit results for display
                hotels = hotel_comparisons[:20]

                search_query.results_count = len(hotels)
                search_query.save()

                messages.success(request, f'Found {len(hotels)} hotel comparisons in {city}')

    recent_searches = SearchQuery.objects.filter(
        user=request.user
    ).order_by('-requested_at')[:5]

    context = {
        'hotels': hotels,
        'city': city,
        'recent_searches': recent_searches,
    }

    return render(request, 'hotels/search.html', context)


@login_required
@require_http_methods(["POST"])
def search_hotels(request):
    """
    Process hotel search and return results.
    """
    form = HotelSearchForm(request.POST)
    
    if not form.is_valid():
        messages.error(request, 'Please correct the search parameters.')
        return redirect('hotels:search')
    
    search_params = form.get_search_params()
    
    # Create search query record
    search_query = SearchQuery.objects.create(
        user=request.user,
        city=search_params['city'],
        min_price=search_params.get('min_price'),
        max_price=search_params.get('max_price'),
        star_rating=search_params.get('star_rating')
    )
    
    # Check for recent results (within last 30 minutes)
    recent_cutoff = timezone.now() - timedelta(minutes=30)
    existing_hotels = Hotel.objects.filter(
        city__iexact=search_params['city'],
        crawl_timestamp__gte=recent_cutoff
    )
    
    if existing_hotels.exists():
        # Use existing recent data
        hotels = filter_hotels(existing_hotels, search_params)
        comparisons = create_comparisons(search_query, hotels)
        search_query.results_count = len(comparisons)
        search_query.save()
        
        messages.success(request, f'Found {len(comparisons)} hotels in {search_params["city"]}')
        return render(request, 'hotels/results.html', {
            'comparisons': comparisons,
            'search_query': search_query,
            'search_params': search_params
        })
    else:
        # Trigger scraping task
        task = scrape_hotels_task.delay(search_params, search_query.id)
        
        # Store task ID in session for polling
        request.session['search_task_id'] = task.id
        request.session['search_query_id'] = search_query.id
        
        messages.info(request, 'Searching for hotels... This may take a few moments.')
        return render(request, 'hotels/searching.html', {
            'search_params': search_params,
            'task_id': task.id,
            'search_query_id': search_query.id
        })


@login_required
def search_status(request, task_id):
    """
    Check the status of a scraping task.
    """
    from celery.result import AsyncResult
    
    task = AsyncResult(task_id)
    
    if task.ready():
        if task.successful():
            search_query_id = request.session.get('search_query_id')
            if search_query_id:
                try:
                    search_query = SearchQuery.objects.get(id=search_query_id)
                    return JsonResponse({
                        'status': 'completed',
                        'redirect_url': f'/hotels/results/{search_query.id}/'
                    })
                except SearchQuery.DoesNotExist:
                    pass
            
            return JsonResponse({
                'status': 'completed',
                'redirect_url': '/hotels/search/'
            })
        else:
            return JsonResponse({
                'status': 'failed',
                'error': str(task.result)
            })
    else:
        return JsonResponse({
            'status': 'pending',
            'message': 'Still searching for hotels...'
        })


@login_required
def results_view(request, search_query_id):
    """
    Display search results.
    """
    try:
        search_query = SearchQuery.objects.get(id=search_query_id, user=request.user)
    except SearchQuery.DoesNotExist:
        messages.error(request, 'Search results not found.')
        return redirect('hotels:search')
    
    comparisons = HotelComparison.objects.filter(
        search_query=search_query
    ).order_by('best_price')
    
    # Pagination
    paginator = Paginator(comparisons, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'comparisons': page_obj,
        'search_query': search_query,
        'total_results': comparisons.count(),
        'page_obj': page_obj
    }
    
    return render(request, 'hotels/results.html', context)


def filter_hotels(hotels_queryset, search_params):
    """
    Filter hotels based on search parameters.
    """
    filtered_hotels = hotels_queryset
    
    if search_params.get('min_price'):
        filtered_hotels = filtered_hotels.filter(price__gte=search_params['min_price'])
    
    if search_params.get('max_price'):
        filtered_hotels = filtered_hotels.filter(price__lte=search_params['max_price'])
    
    if search_params.get('star_rating'):
        filtered_hotels = filtered_hotels.filter(star_rating__gte=search_params['star_rating'])
    
    return filtered_hotels


def create_comparisons(search_query, hotels):
    """
    Create hotel comparisons from filtered hotels.
    """
    # Group hotels by name for comparison
    hotel_groups = {}
    
    for hotel in hotels:
        # Normalize hotel name for grouping
        normalized_name = hotel.name.lower().strip()
        if normalized_name not in hotel_groups:
            hotel_groups[normalized_name] = {
                'name': hotel.name,
                'city': hotel.city,
                'booking': None,
                'agoda': None
            }
        
        hotel_groups[normalized_name][hotel.platform] = hotel
    
    # Create comparisons
    comparisons = []
    
    for group_name, group_data in hotel_groups.items():
        booking_hotel = group_data['booking']
        agoda_hotel = group_data['agoda']
        
        # Determine best price and platform
        if booking_hotel and agoda_hotel:
            if booking_hotel.price <= agoda_hotel.price:
                best_price = booking_hotel.price
                best_platform = 'booking'
                price_difference = agoda_hotel.price - booking_hotel.price
            else:
                best_price = agoda_hotel.price
                best_platform = 'agoda'
                price_difference = booking_hotel.price - agoda_hotel.price
        elif booking_hotel:
            best_price = booking_hotel.price
            best_platform = 'booking'
            price_difference = 0
        elif agoda_hotel:
            best_price = agoda_hotel.price
            best_platform = 'agoda'
            price_difference = 0
        else:
            continue
        
        # Create comparison object
        comparison = HotelComparison.objects.create(
            search_query=search_query,
            hotel_name=group_data['name'],
            city=group_data['city'],
            booking_hotel=booking_hotel,
            agoda_hotel=agoda_hotel,
            best_price=best_price,
            best_platform=best_platform,
            price_difference=price_difference
        )
        
        comparisons.append(comparison)
    
    return comparisons


def create_hotel_comparisons(hotels):
    """
    Create hotel comparisons by grouping similar hotels from different platforms.
    """
    # Group hotels by normalized name
    hotel_groups = {}

    for hotel in hotels:
        # Normalize hotel name for grouping (remove common words, lowercase, etc.)
        normalized_name = normalize_hotel_name(hotel.name)

        if normalized_name not in hotel_groups:
            hotel_groups[normalized_name] = {
                'name': hotel.name,
                'city': hotel.city,
                'booking_hotel': None,
                'agoda_hotel': None,
                'star_rating': hotel.star_rating,
                'image_url': hotel.image_url,
                'address': hotel.address,
                'description': hotel.description,
                'amenities': hotel.amenities,
            }

        # Assign hotel to platform
        if hotel.platform == 'booking':
            hotel_groups[normalized_name]['booking_hotel'] = hotel
        elif hotel.platform == 'agoda':
            hotel_groups[normalized_name]['agoda_hotel'] = hotel

        # Update with better data if available
        if hotel.image_url and not hotel_groups[normalized_name]['image_url']:
            hotel_groups[normalized_name]['image_url'] = hotel.image_url
        if hotel.description and not hotel_groups[normalized_name]['description']:
            hotel_groups[normalized_name]['description'] = hotel.description

    # Create comparison objects
    comparisons = []

    for group_name, group_data in hotel_groups.items():
        booking_hotel = group_data['booking_hotel']
        agoda_hotel = group_data['agoda_hotel']

        # Calculate best price and comparison data
        comparison = {
            'id': f"{group_name}_{group_data['city']}",
            'name': group_data['name'],
            'city': group_data['city'],
            'star_rating': group_data['star_rating'],
            'image_url': group_data['image_url'],
            'address': group_data['address'],
            'description': group_data['description'],
            'amenities': group_data['amenities'],
            'booking_hotel': booking_hotel,
            'agoda_hotel': agoda_hotel,
        }

        # Price comparison logic
        if booking_hotel and agoda_hotel:
            booking_price = float(booking_hotel.price)
            agoda_price = float(agoda_hotel.price)

            if booking_price <= agoda_price:
                comparison['best_price'] = booking_price
                comparison['best_platform'] = 'booking'
                comparison['price_difference'] = agoda_price - booking_price
                comparison['savings'] = agoda_price - booking_price
                comparison['url'] = booking_hotel.url
            else:
                comparison['best_price'] = agoda_price
                comparison['best_platform'] = 'agoda'
                comparison['price_difference'] = booking_price - agoda_price
                comparison['savings'] = booking_price - agoda_price
                comparison['url'] = agoda_hotel.url

            comparison['booking_price'] = booking_price
            comparison['agoda_price'] = agoda_price
            comparison['has_comparison'] = True

        elif booking_hotel:
            comparison['best_price'] = float(booking_hotel.price)
            comparison['best_platform'] = 'booking'
            comparison['price_difference'] = 0
            comparison['savings'] = 0
            comparison['booking_price'] = float(booking_hotel.price)
            comparison['agoda_price'] = None
            comparison['url'] = booking_hotel.url
            comparison['has_comparison'] = False

        elif agoda_hotel:
            comparison['best_price'] = float(agoda_hotel.price)
            comparison['best_platform'] = 'agoda'
            comparison['price_difference'] = 0
            comparison['savings'] = 0
            comparison['booking_price'] = None
            comparison['agoda_price'] = float(agoda_hotel.price)
            comparison['url'] = agoda_hotel.url
            comparison['has_comparison'] = False

        else:
            continue  # Skip if no hotels found

        # Add currency (assume USD for now)
        comparison['currency'] = 'USD'

        comparisons.append(comparison)

    # Sort by best price
    comparisons.sort(key=lambda x: x['best_price'])

    return comparisons


def normalize_hotel_name(name):
    """
    Normalize hotel name for comparison by removing common words and standardizing format.
    """
    import re

    # Convert to lowercase
    normalized = name.lower().strip()

    # Remove common hotel words
    common_words = [
        'hotel', 'inn', 'resort', 'suites', 'lodge', 'motel', 'hostel',
        'the', 'a', 'an', 'and', '&', 'by', 'at', 'in', 'on', 'of'
    ]

    # Split into words and filter
    words = re.findall(r'\b\w+\b', normalized)
    filtered_words = [word for word in words if word not in common_words]

    # Join back and remove extra spaces
    normalized = ' '.join(filtered_words)

    # Remove special characters except spaces
    normalized = re.sub(r'[^\w\s]', '', normalized)

    # Remove extra whitespace
    normalized = ' '.join(normalized.split())

    return normalized
