"""
Admin configuration for scraper app.
"""
from django.contrib import admin
from .models import Scraping<PERSON>ob, ScrapingLog


@admin.register(ScrapingJob)
class ScrapingJobAdmin(admin.ModelAdmin):
    list_display = ['city', 'platform', 'status', 'results_count', 'created_at', 'duration_display']
    list_filter = ['status', 'platform', 'created_at']
    search_fields = ['city']
    readonly_fields = ['created_at', 'started_at', 'completed_at', 'duration_display']
    
    def duration_display(self, obj):
        """Display job duration in a readable format."""
        duration = obj.duration
        if duration:
            total_seconds = int(duration.total_seconds())
            minutes, seconds = divmod(total_seconds, 60)
            return f"{minutes}m {seconds}s"
        return "-"
    duration_display.short_description = 'Duration'


@admin.register(ScrapingLog)
class ScrapingLogAdmin(admin.ModelAdmin):
    list_display = ['job', 'level', 'message_preview', 'timestamp']
    list_filter = ['level', 'timestamp', 'job__platform']
    search_fields = ['message', 'job__city']
    readonly_fields = ['timestamp']
    
    def message_preview(self, obj):
        """Show a preview of the log message."""
        return obj.message[:100] + "..." if len(obj.message) > 100 else obj.message
    message_preview.short_description = 'Message'
