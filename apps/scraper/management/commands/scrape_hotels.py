"""
Django management command to run hotel scraping spiders.
"""
import os
import sys
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from scrapy.crawler import CrawlerProcess
from scrapy.utils.project import get_project_settings
from apps.scraper.models import ScrapingJob, ScrapingLog


class Command(BaseCommand):
    help = 'Run hotel scraping spiders for specified city and platform'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--city',
            type=str,
            required=True,
            help='City to scrape hotels for'
        )
        parser.add_argument(
            '--platform',
            type=str,
            choices=['booking', 'agoda', 'both'],
            default='both',
            help='Platform to scrape (booking, agoda, or both)'
        )
        parser.add_argument(
            '--job-id',
            type=int,
            help='Scraping job ID to track progress'
        )
    
    def handle(self, *args, **options):
        city = options['city']
        platform = options['platform']
        job_id = options.get('job_id')
        
        self.stdout.write(f"Starting hotel scraping for {city} on {platform}")
        
        # Create or get scraping job
        if job_id:
            try:
                job = ScrapingJob.objects.get(id=job_id)
                job.mark_as_running()
            except ScrapingJob.DoesNotExist:
                raise CommandError(f"Scraping job with ID {job_id} not found")
        else:
            job = ScrapingJob.objects.create(
                city=city,
                platform=platform,
                status='running'
            )
            job.mark_as_running()
        
        # Log job start
        ScrapingLog.objects.create(
            job=job,
            level='info',
            message=f"Started scraping {city} on {platform}"
        )
        
        try:
            # Set up Scrapy settings
            scrapy_settings = get_project_settings()
            scrapy_settings.setmodule('apps.scraper.scrapy_project.settings')
            
            # Create crawler process
            process = CrawlerProcess(scrapy_settings)
            
            # Add spiders based on platform choice
            if platform == 'booking' or platform == 'both':
                from apps.scraper.scrapy_project.spiders.booking_spider import BookingSpider
                process.crawl(BookingSpider, city=city, job_id=job.id)
            
            if platform == 'agoda' or platform == 'both':
                from apps.scraper.scrapy_project.spiders.agoda_spider import AgodaSpider
                process.crawl(AgodaSpider, city=city, job_id=job.id)
            
            # Start the crawling process
            process.start()
            
            self.stdout.write(
                self.style.SUCCESS(f"Successfully completed scraping for {city}")
            )
            
        except Exception as e:
            error_msg = f"Error during scraping: {str(e)}"
            self.stdout.write(self.style.ERROR(error_msg))
            
            # Mark job as failed
            job.mark_as_failed(error_msg)
            
            # Log error
            ScrapingLog.objects.create(
                job=job,
                level='error',
                message=error_msg
            )
            
            raise CommandError(error_msg)
