# Generated by Django 4.2.7 on 2025-07-10 18:56

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="ScrapingJob",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("city", models.CharField(max_length=100)),
                (
                    "platform",
                    models.CharField(
                        choices=[
                            ("booking", "Booking.com"),
                            ("agoda", "Agoda"),
                            ("both", "Both Platforms"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("running", "Running"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("started_at", models.DateTimeField(blank=True, null=True)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                ("results_count", models.IntegerField(default=0)),
                ("error_message", models.TextField(blank=True, null=True)),
            ],
            options={
                "verbose_name": "Scraping Job",
                "verbose_name_plural": "Scraping Jobs",
                "db_table": "scraper_scraping_job",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ScrapingLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "level",
                    models.CharField(
                        choices=[
                            ("info", "Info"),
                            ("warning", "Warning"),
                            ("error", "Error"),
                            ("debug", "Debug"),
                        ],
                        max_length=10,
                    ),
                ),
                ("message", models.TextField()),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                (
                    "job",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="logs",
                        to="scraper.scrapingjob",
                    ),
                ),
            ],
            options={
                "verbose_name": "Scraping Log",
                "verbose_name_plural": "Scraping Logs",
                "db_table": "scraper_scraping_log",
                "ordering": ["-timestamp"],
            },
        ),
    ]
