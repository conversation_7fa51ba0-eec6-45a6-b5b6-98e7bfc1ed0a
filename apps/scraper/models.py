"""
Models for the scraper app.
"""
from django.db import models
from django.utils import timezone


class ScrapingJob(models.Model):
    """
    Model to track scraping jobs and their status.
    """
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]
    
    PLATFORM_CHOICES = [
        ('booking', 'Booking.com'),
        ('agoda', 'Agoda'),
        ('both', 'Both Platforms'),
    ]
    
    city = models.CharField(max_length=100)
    platform = models.CharField(max_length=20, choices=PLATFORM_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    created_at = models.DateTimeField(auto_now_add=True)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    results_count = models.IntegerField(default=0)
    error_message = models.TextField(blank=True, null=True)
    
    class Meta:
        db_table = 'scraper_scraping_job'
        verbose_name = 'Scraping Job'
        verbose_name_plural = 'Scraping Jobs'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Scraping {self.city} on {self.platform} - {self.status}"
    
    @property
    def duration(self):
        """Calculate job duration if completed."""
        if self.started_at and self.completed_at:
            return self.completed_at - self.started_at
        return None
    
    def mark_as_running(self):
        """Mark job as running."""
        self.status = 'running'
        self.started_at = timezone.now()
        self.save()
    
    def mark_as_completed(self, results_count=0):
        """Mark job as completed."""
        self.status = 'completed'
        self.completed_at = timezone.now()
        self.results_count = results_count
        self.save()
    
    def mark_as_failed(self, error_message):
        """Mark job as failed."""
        self.status = 'failed'
        self.completed_at = timezone.now()
        self.error_message = error_message
        self.save()


class ScrapingLog(models.Model):
    """
    Model to log scraping activities and errors.
    """
    LOG_LEVEL_CHOICES = [
        ('info', 'Info'),
        ('warning', 'Warning'),
        ('error', 'Error'),
        ('debug', 'Debug'),
    ]
    
    job = models.ForeignKey(ScrapingJob, on_delete=models.CASCADE, related_name='logs')
    level = models.CharField(max_length=10, choices=LOG_LEVEL_CHOICES)
    message = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'scraper_scraping_log'
        verbose_name = 'Scraping Log'
        verbose_name_plural = 'Scraping Logs'
        ordering = ['-timestamp']
    
    def __str__(self):
        return f"{self.level.upper()}: {self.message[:50]}..."
