"""
Define here the models for your scraped items.
"""
import scrapy
from itemloaders.processors import Take<PERSON><PERSON><PERSON>, MapCompose, Join
from w3lib.html import remove_tags


def clean_text(value):
    """Clean text by removing extra whitespace and newlines."""
    if value:
        return ' '.join(value.strip().split())
    return value


def parse_price(value):
    """Extract numeric price from text."""
    if value:
        import re
        # Remove currency symbols and extract numbers
        price_match = re.search(r'[\d,]+\.?\d*', str(value).replace(',', ''))
        if price_match:
            return float(price_match.group())
    return 0.0


def parse_rating(value):
    """Extract numeric rating from text."""
    if value:
        import re
        rating_match = re.search(r'(\d+\.?\d*)', str(value))
        if rating_match:
            rating = float(rating_match.group(1))
            return min(rating, 5.0)  # Cap at 5.0
    return None


class HotelItem(scrapy.Item):
    """Item for hotel data."""
    name = scrapy.Field(
        input_processor=MapCompose(remove_tags, clean_text),
        output_processor=TakeFirst()
    )
    image_url = scrapy.Field(
        output_processor=TakeFirst()
    )
    star_rating = scrapy.Field(
        input_processor=MapCompose(parse_rating),
        output_processor=TakeFirst()
    )
    platform = scrapy.Field(
        output_processor=TakeFirst()
    )
    price = scrapy.Field(
        input_processor=MapCompose(parse_price),
        output_processor=TakeFirst()
    )
    currency = scrapy.Field(
        output_processor=TakeFirst()
    )
    url = scrapy.Field(
        output_processor=TakeFirst()
    )
    city = scrapy.Field(
        input_processor=MapCompose(remove_tags, clean_text),
        output_processor=TakeFirst()
    )
    address = scrapy.Field(
        input_processor=MapCompose(remove_tags, clean_text),
        output_processor=TakeFirst()
    )
    description = scrapy.Field(
        input_processor=MapCompose(remove_tags, clean_text),
        output_processor=TakeFirst()
    )
    amenities = scrapy.Field()
    
    # Metadata fields
    scraped_at = scrapy.Field()
    source_url = scrapy.Field()
    
    def __repr__(self):
        return f"HotelItem(name='{self.get('name', 'N/A')}', platform='{self.get('platform', 'N/A')}', price={self.get('price', 0)})"
