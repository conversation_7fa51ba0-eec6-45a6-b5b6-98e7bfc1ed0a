"""
Define here the models for your spider middleware.
"""
import random
import time
from scrapy import signals
from scrapy.http import HtmlResponse
from fake_useragent import UserAgent


class HotelScraperSpiderMiddleware:
    """Spider middleware for hotel scraper."""
    
    @classmethod
    def from_crawler(cls, crawler):
        s = cls()
        crawler.signals.connect(s.spider_opened, signal=signals.spider_opened)
        return s

    def process_spider_input(self, response, spider):
        return None

    def process_spider_output(self, response, result, spider):
        for i in result:
            yield i

    def process_spider_exception(self, response, exception, spider):
        pass

    def process_start_requests(self, start_requests, spider):
        for r in start_requests:
            yield r

    def spider_opened(self, spider):
        spider.logger.info('Spider opened: %s' % spider.name)


class HotelScraperDownloaderMiddleware:
    """Downloader middleware for hotel scraper."""
    
    @classmethod
    def from_crawler(cls, crawler):
        s = cls()
        crawler.signals.connect(s.spider_opened, signal=signals.spider_opened)
        return s

    def process_request(self, request, spider):
        # Add random delay to avoid being blocked
        delay = random.uniform(1, 3)
        time.sleep(delay)
        return None

    def process_response(self, request, response, spider):
        return response

    def process_exception(self, request, exception, spider):
        pass

    def spider_opened(self, spider):
        spider.logger.info('Spider opened: %s' % spider.name)


class RotateUserAgentMiddleware:
    """Middleware to rotate user agents."""
    
    def __init__(self):
        self.ua = UserAgent()
        
    def process_request(self, request, spider):
        try:
            user_agent = self.ua.random
            request.headers['User-Agent'] = user_agent
        except Exception as e:
            spider.logger.warning(f"Failed to set random user agent: {e}")
            # Fallback to default user agent
            request.headers['User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        return None
