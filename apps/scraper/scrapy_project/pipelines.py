"""
Define your item pipelines here.
"""
import logging
from datetime import datetime
from django.utils import timezone
from apps.hotels.models import Hotel
from apps.scraper.models import ScrapingJob, ScrapingLog


class ValidationPipeline:
    """Pipeline to validate scraped items."""
    
    def process_item(self, item, spider):
        # Validate required fields
        required_fields = ['name', 'platform', 'price', 'url', 'city']
        for field in required_fields:
            if not item.get(field):
                spider.logger.warning(f"Missing required field '{field}' in item: {item}")
                raise DropItem(f"Missing required field: {field}")
        
        # Validate price
        if item.get('price', 0) <= 0:
            spider.logger.warning(f"Invalid price in item: {item}")
            raise DropItem("Invalid price")
        
        # Validate platform
        valid_platforms = ['booking', 'agoda']
        if item.get('platform') not in valid_platforms:
            spider.logger.warning(f"Invalid platform '{item.get('platform')}' in item: {item}")
            raise DropItem("Invalid platform")
        
        return item


class DuplicatesPipeline:
    """Pipeline to filter out duplicate items."""
    
    def __init__(self):
        self.seen_items = set()
    
    def process_item(self, item, spider):
        # Create a unique identifier for the item
        identifier = (
            item.get('name', '').lower().strip(),
            item.get('platform', ''),
            item.get('city', '').lower().strip(),
            item.get('url', '')
        )
        
        if identifier in self.seen_items:
            spider.logger.info(f"Duplicate item found: {item.get('name')}")
            raise DropItem("Duplicate item")
        
        self.seen_items.add(identifier)
        return item


class DjangoPipeline:
    """Pipeline to save items to Django database."""
    
    def __init__(self):
        self.items_count = 0
        self.logger = logging.getLogger(__name__)
    
    def open_spider(self, spider):
        """Called when spider is opened."""
        self.items_count = 0
        spider.logger.info("Django pipeline opened")
    
    def close_spider(self, spider):
        """Called when spider is closed."""
        spider.logger.info(f"Django pipeline closed. Processed {self.items_count} items")
        
        # Update scraping job if available
        if hasattr(spider, 'job_id') and spider.job_id:
            try:
                job = ScrapingJob.objects.get(id=spider.job_id)
                job.mark_as_completed(self.items_count)
                
                # Log completion
                ScrapingLog.objects.create(
                    job=job,
                    level='info',
                    message=f"Scraping completed successfully. Processed {self.items_count} items."
                )
            except ScrapingJob.DoesNotExist:
                spider.logger.warning(f"Scraping job with ID {spider.job_id} not found")
    
    def process_item(self, item, spider):
        try:
            # Convert scrapy item to dict
            item_dict = dict(item)

            # Remove fields that don't exist in the Hotel model
            if 'source_url' in item_dict:
                del item_dict['source_url']
            if 'scraped_at' in item_dict:
                del item_dict['scraped_at']

            # Truncate fields to fit database constraints
            if 'url' in item_dict and item_dict['url']:
                item_dict['url'] = str(item_dict['url'])[:200]
            if 'name' in item_dict and item_dict['name']:
                item_dict['name'] = str(item_dict['name'])[:200]
            if 'address' in item_dict and item_dict['address']:
                item_dict['address'] = str(item_dict['address'])[:500]
            if 'description' in item_dict and item_dict['description']:
                item_dict['description'] = str(item_dict['description'])[:1000]
            if 'image_url' in item_dict and item_dict['image_url']:
                item_dict['image_url'] = str(item_dict['image_url'])[:200]

            # Add timestamp
            item_dict['crawl_timestamp'] = timezone.now()
            
            # Handle amenities (convert list to JSON if needed)
            if 'amenities' in item_dict and isinstance(item_dict['amenities'], list):
                # Keep as list, Django JSONField will handle it
                pass
            elif 'amenities' in item_dict:
                # Convert to list if it's a string
                item_dict['amenities'] = [item_dict['amenities']] if item_dict['amenities'] else []
            else:
                item_dict['amenities'] = []
            
            # Create or update hotel record
            hotel, created = Hotel.objects.update_or_create(
                name=item_dict['name'],
                platform=item_dict['platform'],
                city=item_dict['city'],
                url=item_dict['url'],
                defaults=item_dict
            )
            
            if created:
                spider.logger.info(f"Created new hotel: {hotel.name}")
            else:
                spider.logger.info(f"Updated existing hotel: {hotel.name}")
            
            self.items_count += 1
            
            # Log to scraping job if available
            if hasattr(spider, 'job_id') and spider.job_id:
                try:
                    job = ScrapingJob.objects.get(id=spider.job_id)
                    ScrapingLog.objects.create(
                        job=job,
                        level='info',
                        message=f"Processed hotel: {hotel.name} (${hotel.price})"
                    )
                except ScrapingJob.DoesNotExist:
                    pass
            
            return item
            
        except Exception as e:
            spider.logger.error(f"Error saving item to database: {e}")
            
            # Log error to scraping job if available
            if hasattr(spider, 'job_id') and spider.job_id:
                try:
                    job = ScrapingJob.objects.get(id=spider.job_id)
                    ScrapingLog.objects.create(
                        job=job,
                        level='error',
                        message=f"Error saving item: {str(e)}"
                    )
                except ScrapingJob.DoesNotExist:
                    pass
            
            raise DropItem(f"Error saving item: {e}")


# Import DropItem at the end to avoid circular imports
from scrapy.exceptions import DropItem
