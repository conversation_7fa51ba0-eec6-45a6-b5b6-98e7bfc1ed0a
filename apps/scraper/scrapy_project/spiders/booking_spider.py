"""
Booking.com spider for scraping hotel data.
"""
import scrapy
from urllib.parse import urlencode
from bs4 import BeautifulSoup
from apps.scraper.scrapy_project.items import HotelItem
from itemloaders import ItemLoader


class BookingSpider(scrapy.Spider):
    name = 'booking'
    allowed_domains = ['booking.com']
    
    def __init__(self, city=None, job_id=None, *args, **kwargs):
        super(<PERSON>ing<PERSON>pid<PERSON>, self).__init__(*args, **kwargs)
        self.city = city or 'New York'
        self.job_id = job_id
        self.base_url = 'https://www.booking.com/searchresults.html'
        
    def start_requests(self):
        """Generate initial requests for the spider."""
        if not self.city:
            self.logger.error("No city specified for scraping")
            return
        
        # Parameters for Booking.com search
        params = {
            'ss': self.city,
            'checkin': '2024-12-01',  # Example dates
            'checkout': '2024-12-02',
            'group_adults': '2',
            'no_rooms': '1',
            'offset': '0'
        }
        
        url = f"{self.base_url}?{urlencode(params)}"
        self.logger.info(f"Starting scraping for city: {self.city}")
        self.logger.info(f"Initial URL: {url}")
        
        yield scrapy.Request(
            url=url,
            callback=self.parse,
            meta={'page': 1}
        )
    
    def parse(self, response):
        """Parse the search results page."""
        page = response.meta.get('page', 1)
        self.logger.info(f"Parsing page {page} for {self.city}")
        
        # Use BeautifulSoup for easier HTML parsing
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Find hotel containers (this selector may need adjustment based on current Booking.com structure)
        hotel_containers = soup.find_all('div', {'data-testid': 'property-card'}) or \
                          soup.find_all('div', class_=lambda x: x and 'sr_property_block' in x) or \
                          soup.find_all('div', class_=lambda x: x and 'property_card' in x)
        
        if not hotel_containers:
            self.logger.warning(f"No hotel containers found on page {page}")
            # Try alternative selectors
            hotel_containers = soup.find_all('div', class_=lambda x: x and any(
                keyword in str(x).lower() for keyword in ['hotel', 'property', 'accommodation']
            ))
        
        self.logger.info(f"Found {len(hotel_containers)} hotel containers on page {page}")
        
        for container in hotel_containers:
            try:
                hotel_item = self.extract_hotel_data(container, response.url)
                if hotel_item:
                    yield hotel_item
            except Exception as e:
                self.logger.error(f"Error extracting hotel data: {e}")
                continue
        
        # Follow pagination (limit to 5 pages as per settings)
        if page < 5:
            next_page_link = soup.find('a', {'aria-label': 'Next page'}) or \
                           soup.find('a', class_=lambda x: x and 'next' in str(x).lower())
            
            if next_page_link and next_page_link.get('href'):
                next_url = response.urljoin(next_page_link['href'])
                self.logger.info(f"Following to next page: {page + 1}")
                yield scrapy.Request(
                    url=next_url,
                    callback=self.parse,
                    meta={'page': page + 1}
                )
    
    def extract_hotel_data(self, container, source_url):
        """Extract hotel data from a container element."""
        try:
            loader = ItemLoader(item=HotelItem())
            
            # Hotel name
            name_elem = container.find('h3') or \
                       container.find('a', class_=lambda x: x and 'hotel_name' in str(x)) or \
                       container.find('div', {'data-testid': 'title'})
            
            if name_elem:
                loader.add_value('name', name_elem.get_text(strip=True))
            else:
                return None  # Skip if no name found
            
            # Hotel URL
            link_elem = container.find('a', href=True)
            if link_elem:
                hotel_url = link_elem['href']
                if hotel_url.startswith('/'):
                    hotel_url = f"https://www.booking.com{hotel_url}"
                loader.add_value('url', hotel_url)
            
            # Price
            price_elem = container.find('span', class_=lambda x: x and 'price' in str(x).lower()) or \
                        container.find('div', {'data-testid': 'price-and-discounted-price'}) or \
                        container.find('span', string=lambda text: text and '$' in text)
            
            if price_elem:
                price_text = price_elem.get_text(strip=True)
                loader.add_value('price', price_text)
                # Extract currency (default to USD)
                if '$' in price_text:
                    loader.add_value('currency', 'USD')
                elif '€' in price_text:
                    loader.add_value('currency', 'EUR')
                elif '£' in price_text:
                    loader.add_value('currency', 'GBP')
                else:
                    loader.add_value('currency', 'USD')
            
            # Star rating
            rating_elem = container.find('div', class_=lambda x: x and 'star' in str(x).lower()) or \
                         container.find('span', {'aria-label': lambda x: x and 'star' in str(x).lower()})
            
            if rating_elem:
                rating_text = rating_elem.get_text(strip=True) or rating_elem.get('aria-label', '')
                loader.add_value('star_rating', rating_text)
            
            # Image URL
            img_elem = container.find('img')
            if img_elem and img_elem.get('src'):
                loader.add_value('image_url', img_elem['src'])
            
            # Address/Location
            location_elem = container.find('span', {'data-testid': 'address'}) or \
                           container.find('div', class_=lambda x: x and 'location' in str(x).lower())
            
            if location_elem:
                loader.add_value('address', location_elem.get_text(strip=True))
            
            # Description
            desc_elem = container.find('div', class_=lambda x: x and 'description' in str(x).lower())
            if desc_elem:
                loader.add_value('description', desc_elem.get_text(strip=True))
            
            # Amenities (look for facility/amenity information)
            amenities = []
            amenity_elems = container.find_all('span', class_=lambda x: x and 'facility' in str(x).lower()) or \
                           container.find_all('div', class_=lambda x: x and 'amenity' in str(x).lower())
            
            for amenity_elem in amenity_elems:
                amenity_text = amenity_elem.get_text(strip=True)
                if amenity_text:
                    amenities.append(amenity_text)
            
            loader.add_value('amenities', amenities)
            
            # Platform and city
            loader.add_value('platform', 'booking')
            loader.add_value('city', self.city)
            loader.add_value('source_url', source_url)
            
            return loader.load_item()
            
        except Exception as e:
            self.logger.error(f"Error in extract_hotel_data: {e}")
            return None
