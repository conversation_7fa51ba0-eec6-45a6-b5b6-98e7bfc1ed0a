"""
Celery tasks for the scraper app.
"""
import os
import sys
from celery import shared_task
from django.conf import settings
from scrapy.crawler import CrawlerProcess
from scrapy.utils.project import get_project_settings
from .models import ScrapingJob, ScrapingLog


@shared_task
def scrape_hotels_task(city, platform='both', job_id=None):
    """
    Celery task to run hotel scraping spiders.
    
    Args:
        city (str): City to scrape hotels for
        platform (str): Platform to scrape ('booking', 'agoda', or 'both')
        job_id (int): Optional scraping job ID to track progress
    
    Returns:
        dict: Task result with status and message
    """
    try:
        # Create or get scraping job
        if job_id:
            try:
                job = ScrapingJob.objects.get(id=job_id)
                job.mark_as_running()
            except ScrapingJob.DoesNotExist:
                job = ScrapingJob.objects.create(
                    city=city,
                    platform=platform,
                    status='running'
                )
                job.mark_as_running()
        else:
            job = ScrapingJob.objects.create(
                city=city,
                platform=platform,
                status='running'
            )
            job.mark_as_running()
        
        # Log job start
        ScrapingLog.objects.create(
            job=job,
            level='info',
            message=f"Started scraping {city} on {platform}"
        )
        
        # Set up Scrapy settings
        scrapy_settings = get_project_settings()
        scrapy_settings.setmodule('apps.scraper.scrapy_project.settings')
        
        # Create crawler process
        process = CrawlerProcess(scrapy_settings)
        
        # Add spiders based on platform choice
        if platform == 'booking' or platform == 'both':
            from apps.scraper.scrapy_project.spiders.booking_spider import BookingSpider
            process.crawl(BookingSpider, city=city, job_id=job.id)
        
        if platform == 'agoda' or platform == 'both':
            from apps.scraper.scrapy_project.spiders.agoda_spider import AgodaSpider
            process.crawl(AgodaSpider, city=city, job_id=job.id)
        
        # Start the crawling process
        process.start()
        
        return {
            'status': 'success',
            'message': f'Successfully completed scraping for {city}',
            'job_id': job.id
        }
        
    except Exception as e:
        error_msg = f"Error during scraping: {str(e)}"
        
        # Mark job as failed if we have one
        if 'job' in locals():
            job.mark_as_failed(error_msg)
            
            # Log error
            ScrapingLog.objects.create(
                job=job,
                level='error',
                message=error_msg
            )
        
        return {
            'status': 'error',
            'message': error_msg,
            'job_id': job.id if 'job' in locals() else None
        }


@shared_task
def cleanup_old_scraping_logs():
    """
    Celery task to cleanup old scraping logs (older than 30 days).
    """
    from django.utils import timezone
    from datetime import timedelta
    
    try:
        cutoff_date = timezone.now() - timedelta(days=30)
        deleted_count = ScrapingLog.objects.filter(timestamp__lt=cutoff_date).delete()[0]
        
        return {
            'status': 'success',
            'message': f'Cleaned up {deleted_count} old scraping logs'
        }
    except Exception as e:
        return {
            'status': 'error',
            'message': f'Error cleaning up logs: {str(e)}'
        }


@shared_task
def test_scraping_connection():
    """
    Test task to verify scraping setup is working.
    """
    try:
        # Test database connection
        job_count = ScrapingJob.objects.count()
        
        # Test Scrapy settings
        scrapy_settings = get_project_settings()
        scrapy_settings.setmodule('apps.scraper.scrapy_project.settings')
        
        return {
            'status': 'success',
            'message': f'Scraping setup is working. Found {job_count} scraping jobs in database.',
            'scrapy_settings_loaded': True
        }
    except Exception as e:
        return {
            'status': 'error',
            'message': f'Scraping setup test failed: {str(e)}'
        }
