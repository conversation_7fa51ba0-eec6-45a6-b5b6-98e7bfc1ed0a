version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: hotelcompare_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: hotelcompare
      POSTGRES_USER: hotel_user
      POSTGRES_PASSWORD: secure_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - hotelcompare_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U hotel_user -d hotelcompare"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for Celery
  redis:
    image: redis:7-alpine
    container_name: hotelcompare_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - hotelcompare_network
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # pgAdmin (Optional - for database management)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: hotelcompare_pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - hotelcompare_network
    depends_on:
      - postgres

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  hotelcompare_network:
    driver: bridge
