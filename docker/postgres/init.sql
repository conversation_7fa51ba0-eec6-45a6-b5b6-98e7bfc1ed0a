-- PostgreSQL initialization script for Hotel Compare project
-- This script runs when the PostgreSQL container is first created

-- Create additional extensions if needed
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create indexes for better performance (these will be created by Django migrations)
-- This is just a placeholder for any custom database setup

-- Set timezone
SET timezone = 'UTC';

-- Log initialization
DO $$
BEGIN
    RAISE NOTICE 'Hotel Compare PostgreSQL database initialized successfully';
END $$;
