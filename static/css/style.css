/* Custom CSS for Hotel Compare */

/* General Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

main {
    flex: 1;
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* Hotel Cards */
.hotel-card {
    margin-bottom: 1.5rem;
}

.hotel-image {
    height: 200px;
    object-fit: cover;
    border-radius: 0.375rem 0.375rem 0 0;
}

.price-comparison {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-top: 1rem;
}

.best-price {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    color: white;
    font-weight: bold;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    display: inline-block;
}

.platform-price {
    background-color: #6c757d;
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 0.25rem;
    margin: 0.2rem;
    display: inline-block;
    font-size: 0.9rem;
}

/* Star Rating */
.star-rating {
    color: #ffc107;
    font-size: 1.1rem;
}

.star-rating .empty {
    color: #e9ecef;
}

/* Search Form */
.search-form {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.search-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 0;
    margin-bottom: 2rem;
}

/* Buttons */
.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    padding: 0.75rem 2rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-outline-primary {
    border-color: #667eea;
    color: #667eea;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
}

/* Bookmark Button */
.bookmark-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.bookmark-btn:hover {
    background: white;
    transform: scale(1.1);
}

.bookmark-btn.bookmarked {
    color: #dc3545;
}

/* Loading Spinner */
.loading-spinner {
    display: none;
    text-align: center;
    padding: 2rem;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Results Section */
.results-header {
    background: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.no-results {
    text-align: center;
    padding: 3rem;
    color: #6c757d;
}

/* Login/Register Forms */
.auth-container {
    min-height: 80vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.auth-card {
    max-width: 400px;
    width: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
    .search-hero {
        padding: 2rem 0;
    }

    .search-form {
        padding: 1.5rem;
        margin: 1rem;
    }

    .hotel-image {
        height: 150px;
    }

    .price-comparison {
        padding: 0.75rem;
    }

    .navbar-brand {
        font-size: 1.2rem;
    }

    .card-title {
        font-size: 1.1rem;
    }

    .btn {
        font-size: 0.9rem;
    }

    .results-header h3 {
        font-size: 1.3rem;
    }
}

@media (max-width: 576px) {
    .search-hero h1 {
        font-size: 2rem;
    }

    .search-hero .lead {
        font-size: 1rem;
    }

    .search-form {
        padding: 1rem;
        margin: 0.5rem;
    }

    .hotel-card {
        margin-bottom: 1rem;
    }

    .price-comparison {
        font-size: 0.9rem;
    }

    .platform-price, .best-price {
        font-size: 0.8rem;
        padding: 0.25rem 0.6rem;
    }

    .bookmark-btn {
        width: 35px;
        height: 35px;
    }

    .auth-container {
        padding: 1rem;
    }

    .auth-card {
        margin: 0 1rem;
    }
}

/* Touch-friendly improvements */
@media (hover: none) and (pointer: coarse) {
    .btn {
        min-height: 44px;
        padding: 0.75rem 1.5rem;
    }

    .bookmark-btn {
        min-width: 44px;
        min-height: 44px;
    }

    .form-control {
        min-height: 44px;
    }

    .form-select {
        min-height: 44px;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-custom {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Form Enhancements */
.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 500;
    color: #495057;
}

/* Alert Enhancements */
.alert {
    border: none;
    border-radius: 0.5rem;
}

/* Footer */
footer {
    margin-top: auto;
}
