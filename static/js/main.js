// Main JavaScript for Hotel Compare

$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Search form handling
    $('#search-form').on('submit', function(e) {
        e.preventDefault();
        performSearch();
    });

    // Bookmark functionality
    $('.bookmark-btn').on('click', function(e) {
        e.preventDefault();
        toggleBookmark($(this));
    });

    // Price range slider
    $('#price-range').on('input', function() {
        updatePriceDisplay();
    });

    // Auto-dismiss alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
});

// Search functionality
function performSearch() {
    const form = $('#search-form');
    const submitBtn = form.find('button[type="submit"]');
    const originalText = submitBtn.html();
    
    // Show loading state
    submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>Searching...');
    submitBtn.prop('disabled', true);
    
    // Show loading spinner
    $('#loading-spinner').show();
    $('#search-results').hide();
    
    // Get form data
    const formData = {
        city: $('#city').val(),
        min_price: $('#min_price').val(),
        max_price: $('#max_price').val(),
        star_rating: $('#star_rating').val(),
        csrfmiddlewaretoken: $('[name=csrfmiddlewaretoken]').val()
    };
    
    // Perform AJAX search
    $.ajax({
        url: form.attr('action'),
        method: 'POST',
        data: formData,
        success: function(response) {
            displaySearchResults(response);
        },
        error: function(xhr, status, error) {
            showAlert('Error performing search. Please try again.', 'danger');
            console.error('Search error:', error);
        },
        complete: function() {
            // Reset button state
            submitBtn.html(originalText);
            submitBtn.prop('disabled', false);
            $('#loading-spinner').hide();
        }
    });
}

// Display search results
function displaySearchResults(response) {
    const resultsContainer = $('#search-results');
    
    if (response.hotels && response.hotels.length > 0) {
        let resultsHtml = `
            <div class="results-header">
                <h3><i class="fas fa-search me-2"></i>Search Results</h3>
                <p class="mb-0">Found ${response.hotels.length} hotels in ${response.city}</p>
            </div>
        `;
        
        response.hotels.forEach(function(hotel) {
            resultsHtml += createHotelCard(hotel);
        });
        
        resultsContainer.html(resultsHtml);
    } else {
        resultsContainer.html(`
            <div class="no-results">
                <i class="fas fa-search fa-3x mb-3 text-muted"></i>
                <h4>No hotels found</h4>
                <p>Try adjusting your search criteria</p>
            </div>
        `);
    }
    
    resultsContainer.show().addClass('fade-in');
    
    // Scroll to results
    $('html, body').animate({
        scrollTop: resultsContainer.offset().top - 100
    }, 500);
}

// Create hotel card HTML
function createHotelCard(hotel) {
    const starRating = generateStarRating(hotel.star_rating);
    const priceComparison = generatePriceComparison(hotel);
    const bookmarkClass = hotel.is_bookmarked ? 'bookmarked' : '';
    
    return `
        <div class="col-md-6 col-lg-4 hotel-card">
            <div class="card h-100 position-relative">
                <button class="bookmark-btn ${bookmarkClass}" data-hotel-id="${hotel.id}" title="Bookmark this hotel">
                    <i class="fas fa-heart"></i>
                </button>
                
                <img src="${hotel.image_url || '/static/images/hotel-placeholder.jpg'}" 
                     class="hotel-image" alt="${hotel.name}">
                
                <div class="card-body">
                    <h5 class="card-title">${hotel.name}</h5>
                    <div class="star-rating mb-2">${starRating}</div>
                    
                    ${priceComparison}
                    
                    <div class="mt-3">
                        <a href="${hotel.booking_url}" target="_blank" class="btn btn-primary btn-sm me-2">
                            <i class="fas fa-external-link-alt me-1"></i>Book Now
                        </a>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Generate star rating HTML
function generateStarRating(rating) {
    let stars = '';
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    
    for (let i = 0; i < fullStars; i++) {
        stars += '<i class="fas fa-star"></i>';
    }
    
    if (hasHalfStar) {
        stars += '<i class="fas fa-star-half-alt"></i>';
    }
    
    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
        stars += '<i class="far fa-star empty"></i>';
    }
    
    return stars + ` <span class="ms-1">(${rating})</span>`;
}

// Generate price comparison HTML
function generatePriceComparison(hotel) {
    let html = '<div class="price-comparison">';
    html += '<h6><i class="fas fa-dollar-sign me-1"></i>Price Comparison</h6>';

    if (hotel.has_comparison && hotel.booking_price && hotel.agoda_price) {
        const bookingPrice = parseFloat(hotel.booking_price);
        const agodaPrice = parseFloat(hotel.agoda_price);
        const savings = hotel.savings || 0;

        if (hotel.best_platform === 'booking') {
            html += `<span class="best-price"><i class="fas fa-bed me-1"></i>Booking.com: $${bookingPrice}</span><br>`;
            html += `<span class="platform-price"><i class="fas fa-suitcase me-1"></i>Agoda: $${agodaPrice}</span>`;
            if (savings > 0) {
                html += `<div class="mt-1"><small class="text-success"><i class="fas fa-piggy-bank me-1"></i>Save $${savings.toFixed(0)}</small></div>`;
            }
        } else {
            html += `<span class="platform-price"><i class="fas fa-bed me-1"></i>Booking.com: $${bookingPrice}</span><br>`;
            html += `<span class="best-price"><i class="fas fa-suitcase me-1"></i>Agoda: $${agodaPrice}</span>`;
            if (savings > 0) {
                html += `<div class="mt-1"><small class="text-success"><i class="fas fa-piggy-bank me-1"></i>Save $${savings.toFixed(0)}</small></div>`;
            }
        }
    } else if (hotel.best_platform === 'booking') {
        html += `<span class="best-price"><i class="fas fa-bed me-1"></i>Booking.com: $${hotel.best_price}</span>`;
    } else if (hotel.best_platform === 'agoda') {
        html += `<span class="best-price"><i class="fas fa-suitcase me-1"></i>Agoda: $${hotel.best_price}</span>`;
    } else {
        html += '<span class="platform-price">Price not available</span>';
    }

    html += '</div>';
    return html;
}

// Toggle bookmark
function toggleBookmark(button) {
    const hotelId = button.data('hotel-id');
    const isBookmarked = button.hasClass('bookmarked');
    
    $.ajax({
        url: '/bookmarks/toggle/',
        method: 'POST',
        data: {
            hotel_id: hotelId,
            csrfmiddlewaretoken: $('[name=csrfmiddlewaretoken]').val()
        },
        success: function(response) {
            if (response.success) {
                if (response.bookmarked) {
                    button.addClass('bookmarked');
                    showAlert('Hotel bookmarked successfully!', 'success');
                } else {
                    button.removeClass('bookmarked');
                    showAlert('Hotel removed from bookmarks.', 'info');
                }
            } else {
                showAlert('Error updating bookmark. Please try again.', 'danger');
            }
        },
        error: function() {
            showAlert('Error updating bookmark. Please try again.', 'danger');
        }
    });
}

// Update price display
function updatePriceDisplay() {
    const minPrice = $('#min_price').val();
    const maxPrice = $('#max_price').val();
    $('#price-display').text(`$${minPrice} - $${maxPrice}`);
}

// Show alert message
function showAlert(message, type) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('.container').first().prepend(alertHtml);
    
    // Auto-dismiss after 5 seconds
    setTimeout(function() {
        $('.alert').first().fadeOut();
    }, 5000);
}

// Form validation
function validateSearchForm() {
    const city = $('#city').val().trim();
    const minPrice = parseInt($('#min_price').val());
    const maxPrice = parseInt($('#max_price').val());
    
    if (!city) {
        showAlert('Please enter a city name.', 'warning');
        return false;
    }
    
    if (minPrice >= maxPrice) {
        showAlert('Minimum price must be less than maximum price.', 'warning');
        return false;
    }
    
    return true;
}
