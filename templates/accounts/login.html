{% extends 'base.html' %}
{% load static %}

{% block title %}Login - Hotel Compare{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="auth-card">
        <div class="card shadow-custom">
            <div class="card-body p-4">
                <div class="text-center mb-4">
                    <i class="fas fa-hotel fa-3x text-gradient mb-3"></i>
                    <h2 class="text-gradient">Welcome Back</h2>
                    <p class="text-muted">Sign in to your account</p>
                </div>

                <form method="post" id="login-form">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="username" class="form-label">
                            <i class="fas fa-user me-1"></i>Username
                        </label>
                        <input type="text" class="form-control" id="username" name="username" 
                               placeholder="Enter your username" required>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock me-1"></i>Password
                        </label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="password" name="password" 
                                   placeholder="Enter your password" required>
                            <button class="btn btn-outline-secondary" type="button" id="toggle-password">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                        <label class="form-check-label" for="remember_me">
                            Remember me
                        </label>
                    </div>

                    <button type="submit" class="btn btn-primary w-100 mb-3">
                        <i class="fas fa-sign-in-alt me-2"></i>Sign In
                    </button>
                </form>

                <div class="text-center">
                    <p class="mb-0">Don't have an account? 
                        <a href="{% url 'accounts:register' %}" class="text-decoration-none">
                            Sign up here
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Toggle password visibility
    $('#toggle-password').on('click', function() {
        const passwordField = $('#password');
        const icon = $(this).find('i');
        
        if (passwordField.attr('type') === 'password') {
            passwordField.attr('type', 'text');
            icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            passwordField.attr('type', 'password');
            icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });

    // Form validation
    $('#login-form').on('submit', function(e) {
        const username = $('#username').val().trim();
        const password = $('#password').val();

        if (!username) {
            e.preventDefault();
            showAlert('Please enter your username.', 'warning');
            return;
        }

        if (!password) {
            e.preventDefault();
            showAlert('Please enter your password.', 'warning');
            return;
        }
    });
});
</script>
{% endblock %}
