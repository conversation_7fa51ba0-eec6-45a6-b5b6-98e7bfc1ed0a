{% extends 'base.html' %}
{% load static %}

{% block title %}Register - Hotel Compare{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="auth-card">
        <div class="card shadow-custom">
            <div class="card-body p-4">
                <div class="text-center mb-4">
                    <i class="fas fa-user-plus fa-3x text-gradient mb-3"></i>
                    <h2 class="text-gradient">Create Account</h2>
                    <p class="text-muted">Join us to start comparing hotels</p>
                </div>

                <form method="post" id="register-form">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="first_name" class="form-label">
                                <i class="fas fa-user me-1"></i>First Name
                            </label>
                            <input type="text" class="form-control" id="first_name" name="first_name" 
                                   placeholder="Enter your first name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="last_name" class="form-label">
                                <i class="fas fa-user me-1"></i>Last Name
                            </label>
                            <input type="text" class="form-control" id="last_name" name="last_name" 
                                   placeholder="Enter your last name" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="username" class="form-label">
                            <i class="fas fa-at me-1"></i>Username
                        </label>
                        <input type="text" class="form-control" id="username" name="username" 
                               placeholder="Choose a username" required>
                        <div class="form-text">Username must be unique and contain only letters, numbers, and underscores.</div>
                    </div>

                    <div class="mb-3">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope me-1"></i>Email Address
                        </label>
                        <input type="email" class="form-control" id="email" name="email" 
                               placeholder="Enter your email address" required>
                    </div>

                    <div class="mb-3">
                        <label for="password1" class="form-label">
                            <i class="fas fa-lock me-1"></i>Password
                        </label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="password1" name="password1" 
                                   placeholder="Create a password" required>
                            <button class="btn btn-outline-secondary" type="button" id="toggle-password1">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="form-text">Password must be at least 8 characters long.</div>
                    </div>

                    <div class="mb-3">
                        <label for="password2" class="form-label">
                            <i class="fas fa-lock me-1"></i>Confirm Password
                        </label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="password2" name="password2" 
                                   placeholder="Confirm your password" required>
                            <button class="btn btn-outline-secondary" type="button" id="toggle-password2">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="terms" name="terms" required>
                        <label class="form-check-label" for="terms">
                            I agree to the <a href="#" class="text-decoration-none">Terms of Service</a> 
                            and <a href="#" class="text-decoration-none">Privacy Policy</a>
                        </label>
                    </div>

                    <button type="submit" class="btn btn-primary w-100 mb-3">
                        <i class="fas fa-user-plus me-2"></i>Create Account
                    </button>
                </form>

                <div class="text-center">
                    <p class="mb-0">Already have an account? 
                        <a href="{% url 'accounts:login' %}" class="text-decoration-none">
                            Sign in here
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Toggle password visibility
    $('#toggle-password1, #toggle-password2').on('click', function() {
        const targetId = $(this).attr('id').replace('toggle-', '');
        const passwordField = $('#' + targetId);
        const icon = $(this).find('i');
        
        if (passwordField.attr('type') === 'password') {
            passwordField.attr('type', 'text');
            icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            passwordField.attr('type', 'password');
            icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });

    // Real-time password confirmation check
    $('#password2').on('input', function() {
        const password1 = $('#password1').val();
        const password2 = $(this).val();
        
        if (password2 && password1 !== password2) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });

    // Form validation
    $('#register-form').on('submit', function(e) {
        const password1 = $('#password1').val();
        const password2 = $('#password2').val();
        const email = $('#email').val();
        const username = $('#username').val();

        // Check password match
        if (password1 !== password2) {
            e.preventDefault();
            showAlert('Passwords do not match.', 'warning');
            return;
        }

        // Check password length
        if (password1.length < 8) {
            e.preventDefault();
            showAlert('Password must be at least 8 characters long.', 'warning');
            return;
        }

        // Check email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            e.preventDefault();
            showAlert('Please enter a valid email address.', 'warning');
            return;
        }

        // Check username format
        const usernameRegex = /^[a-zA-Z0-9_]+$/;
        if (!usernameRegex.test(username)) {
            e.preventDefault();
            showAlert('Username can only contain letters, numbers, and underscores.', 'warning');
            return;
        }
    });
});
</script>
{% endblock %}
