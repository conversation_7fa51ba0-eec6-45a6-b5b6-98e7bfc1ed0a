{% extends 'base.html' %}
{% load static %}

{% block title %}My Bookmarks - Hotel Compare{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="text-gradient">
                        <i class="fas fa-bookmark me-2"></i>My Bookmarks
                    </h1>
                    <p class="text-muted mb-0">Your saved hotels for future reference</p>
                </div>
                <div>
                    <a href="{% url 'hotels:search' %}" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>Search More Hotels
                    </a>
                </div>
            </div>
        </div>
    </div>

    {% if bookmarks %}
        <!-- Bookmarks Count -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    You have {{ bookmarks|length }} bookmarked hotel{{ bookmarks|length|pluralize }}.
                </div>
            </div>
        </div>

        <!-- Filter and Sort Options -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-filter"></i>
                    </span>
                    <select class="form-select" id="platform-filter">
                        <option value="">All Platforms</option>
                        <option value="booking">Booking.com</option>
                        <option value="agoda">Agoda</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-sort"></i>
                    </span>
                    <select class="form-select" id="sort-options">
                        <option value="date-desc">Recently Added</option>
                        <option value="date-asc">Oldest First</option>
                        <option value="price-asc">Price: Low to High</option>
                        <option value="price-desc">Price: High to Low</option>
                        <option value="rating-desc">Rating: High to Low</option>
                        <option value="name-asc">Name: A to Z</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Bookmarked Hotels -->
        <div class="row" id="bookmarks-container">
            {% for bookmark in bookmarks %}
                <div class="col-md-6 col-lg-4 bookmark-item" 
                     data-platform="{{ bookmark.hotel.platform }}"
                     data-price="{{ bookmark.hotel.price }}"
                     data-rating="{{ bookmark.hotel.star_rating }}"
                     data-name="{{ bookmark.hotel.name|lower }}"
                     data-date="{{ bookmark.created_at|date:'Y-m-d' }}">
                    
                    <div class="card h-100 position-relative mb-4">
                        <!-- Remove Bookmark Button -->
                        <button class="bookmark-btn bookmarked" 
                                data-hotel-id="{{ bookmark.hotel.id }}" 
                                title="Remove from bookmarks">
                            <i class="fas fa-heart"></i>
                        </button>
                        
                        <!-- Bookmark Date Badge -->
                        <div class="position-absolute top-0 start-0 m-2">
                            <span class="badge bg-secondary">
                                <i class="fas fa-calendar me-1"></i>
                                {{ bookmark.created_at|date:"M d, Y" }}
                            </span>
                        </div>
                        
                        {% if bookmark.hotel.image_url %}
                            <img src="{{ bookmark.hotel.image_url }}" class="hotel-image" alt="{{ bookmark.hotel.name }}">
                        {% else %}
                            <div class="hotel-image bg-light d-flex align-items-center justify-content-center">
                                <i class="fas fa-hotel fa-3x text-muted"></i>
                            </div>
                        {% endif %}
                        
                        <div class="card-body">
                            <h5 class="card-title">{{ bookmark.hotel.name }}</h5>
                            
                            <!-- Location -->
                            <p class="text-muted mb-2">
                                <i class="fas fa-map-marker-alt me-1"></i>{{ bookmark.hotel.city }}
                            </p>
                            
                            <!-- Star Rating -->
                            <div class="star-rating mb-2">
                                {% for i in "12345" %}
                                    {% if forloop.counter <= bookmark.hotel.star_rating %}
                                        <i class="fas fa-star"></i>
                                    {% else %}
                                        <i class="far fa-star empty"></i>
                                    {% endif %}
                                {% endfor %}
                                <span class="ms-1">({{ bookmark.hotel.star_rating }})</span>
                            </div>
                            
                            <!-- Platform Badge -->
                            <div class="mb-2">
                                {% if bookmark.hotel.platform == 'booking' %}
                                    <span class="badge bg-primary">
                                        <i class="fas fa-bed me-1"></i>Booking.com
                                    </span>
                                {% elif bookmark.hotel.platform == 'agoda' %}
                                    <span class="badge bg-success">
                                        <i class="fas fa-suitcase me-1"></i>Agoda
                                    </span>
                                {% endif %}
                            </div>
                            
                            <!-- Price -->
                            <div class="price-comparison">
                                <h6><i class="fas fa-dollar-sign me-1"></i>Price</h6>
                                <span class="best-price">${{ bookmark.hotel.price }}</span>
                            </div>
                            
                            <!-- Description -->
                            {% if bookmark.hotel.description %}
                                <p class="card-text mt-2">
                                    {{ bookmark.hotel.description|truncatewords:15 }}
                                </p>
                            {% endif %}
                            
                            <!-- Actions -->
                            <div class="mt-3 d-flex gap-2">
                                <a href="{{ bookmark.hotel.url }}" target="_blank" class="btn btn-primary btn-sm flex-fill">
                                    <i class="fas fa-external-link-alt me-1"></i>Book Now
                                </a>
                                <button class="btn btn-outline-danger btn-sm" 
                                        onclick="removeBookmark({{ bookmark.hotel.id }})"
                                        title="Remove bookmark">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <!-- Empty State -->
        <div class="text-center py-5">
            <i class="fas fa-bookmark fa-5x text-muted mb-4"></i>
            <h3 class="text-muted">No Bookmarks Yet</h3>
            <p class="text-muted mb-4">Start searching for hotels and bookmark your favorites!</p>
            <a href="{% url 'hotels:search' %}" class="btn btn-primary btn-lg">
                <i class="fas fa-search me-2"></i>Search Hotels
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Platform filter
    $('#platform-filter').on('change', function() {
        filterBookmarks();
    });
    
    // Sort options
    $('#sort-options').on('change', function() {
        sortBookmarks();
    });
    
    // Initialize bookmark buttons
    $('.bookmark-btn').on('click', function(e) {
        e.preventDefault();
        toggleBookmark($(this));
    });
});

function filterBookmarks() {
    const selectedPlatform = $('#platform-filter').val();
    const bookmarkItems = $('.bookmark-item');
    
    bookmarkItems.each(function() {
        const platform = $(this).data('platform');
        
        if (!selectedPlatform || platform === selectedPlatform) {
            $(this).show().addClass('fade-in');
        } else {
            $(this).hide();
        }
    });
}

function sortBookmarks() {
    const sortBy = $('#sort-options').val();
    const container = $('#bookmarks-container');
    const items = container.children('.bookmark-item').get();
    
    items.sort(function(a, b) {
        const aData = $(a).data();
        const bData = $(b).data();
        
        switch(sortBy) {
            case 'date-desc':
                return new Date(bData.date) - new Date(aData.date);
            case 'date-asc':
                return new Date(aData.date) - new Date(bData.date);
            case 'price-asc':
                return parseFloat(aData.price) - parseFloat(bData.price);
            case 'price-desc':
                return parseFloat(bData.price) - parseFloat(aData.price);
            case 'rating-desc':
                return parseFloat(bData.rating) - parseFloat(aData.rating);
            case 'name-asc':
                return aData.name.localeCompare(bData.name);
            default:
                return 0;
        }
    });
    
    $.each(items, function(index, item) {
        container.append(item);
    });
}

function removeBookmark(hotelId) {
    if (confirm('Are you sure you want to remove this bookmark?')) {
        $.ajax({
            url: '/bookmarks/toggle/',
            method: 'POST',
            data: {
                hotel_id: hotelId,
                csrfmiddlewaretoken: $('[name=csrfmiddlewaretoken]').val()
            },
            success: function(response) {
                if (response.success && !response.bookmarked) {
                    // Remove the bookmark item from the page
                    $(`.bookmark-item[data-hotel-id="${hotelId}"]`).fadeOut(function() {
                        $(this).remove();
                        
                        // Check if no bookmarks left
                        if ($('.bookmark-item').length === 0) {
                            location.reload();
                        }
                    });
                    
                    showAlert('Bookmark removed successfully!', 'success');
                }
            },
            error: function() {
                showAlert('Error removing bookmark. Please try again.', 'danger');
            }
        });
    }
}
</script>
{% endblock %}
