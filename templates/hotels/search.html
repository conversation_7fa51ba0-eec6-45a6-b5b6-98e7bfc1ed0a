{% extends 'base.html' %}
{% load static %}

{% block title %}Search Hotels - Hotel Compare{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="search-hero">
    <div class="container text-center">
        <h1 class="display-4 fw-bold mb-3">
            <i class="fas fa-search me-3"></i>Find Your Perfect Hotel
        </h1>
        <p class="lead mb-0">Compare prices from Booking.com and Agoda to get the best deals</p>
    </div>
</div>

<div class="container">
    <!-- Search Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="search-form">
                <form id="search-form" method="post" action="{% url 'hotels:search' %}">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="city" class="form-label">
                                <i class="fas fa-map-marker-alt me-1"></i>City
                            </label>
                            <input type="text" class="form-control form-control-lg" id="city" name="city" 
                                   placeholder="Enter city name (e.g., New York, Paris)" required
                                   value="{{ request.POST.city|default:'' }}">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="star_rating" class="form-label">
                                <i class="fas fa-star me-1"></i>Minimum Star Rating
                            </label>
                            <select class="form-select form-select-lg" id="star_rating" name="star_rating">
                                <option value="">Any Rating</option>
                                <option value="1" {% if request.POST.star_rating == "1" %}selected{% endif %}>1+ Stars</option>
                                <option value="2" {% if request.POST.star_rating == "2" %}selected{% endif %}>2+ Stars</option>
                                <option value="3" {% if request.POST.star_rating == "3" %}selected{% endif %}>3+ Stars</option>
                                <option value="4" {% if request.POST.star_rating == "4" %}selected{% endif %}>4+ Stars</option>
                                <option value="5" {% if request.POST.star_rating == "5" %}selected{% endif %}>5 Stars</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="min_price" class="form-label">
                                <i class="fas fa-dollar-sign me-1"></i>Minimum Price ($)
                            </label>
                            <input type="number" class="form-control form-control-lg" id="min_price" name="min_price" 
                                   min="0" max="10000" step="10" placeholder="0"
                                   value="{{ request.POST.min_price|default:'0' }}">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="max_price" class="form-label">
                                <i class="fas fa-dollar-sign me-1"></i>Maximum Price ($)
                            </label>
                            <input type="number" class="form-control form-control-lg" id="max_price" name="max_price" 
                                   min="0" max="10000" step="10" placeholder="1000"
                                   value="{{ request.POST.max_price|default:'1000' }}">
                        </div>
                    </div>
                    
                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-primary btn-lg px-5">
                            <i class="fas fa-search me-2"></i>Search Hotels
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div id="loading-spinner" class="loading-spinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-3 text-muted">Searching for the best hotel deals...</p>
    </div>

    <!-- Search Results -->
    <div id="search-results" style="display: none;">
        {% if hotels %}
            <div class="results-header">
                <h3><i class="fas fa-search me-2"></i>Search Results</h3>
                <p class="mb-0">Found {{ hotels|length }} hotels in {{ city }}</p>
            </div>
            
            <div class="row">
                {% for hotel in hotels %}
                    <div class="col-md-6 col-lg-4 hotel-card">
                        <div class="card h-100 position-relative">
                            <button class="bookmark-btn {% if hotel.is_bookmarked %}bookmarked{% endif %}"
                                    data-hotel-id="{% if hotel.best_platform == 'booking' and hotel.booking_hotel %}{{ hotel.booking_hotel.id }}{% elif hotel.agoda_hotel %}{{ hotel.agoda_hotel.id }}{% endif %}"
                                    title="Bookmark this hotel">
                                <i class="fas fa-heart"></i>
                            </button>
                            
                            {% if hotel.image_url %}
                                <img src="{{ hotel.image_url }}" class="hotel-image" alt="{{ hotel.name }}">
                            {% else %}
                                <div class="hotel-image bg-light d-flex align-items-center justify-content-center">
                                    <i class="fas fa-hotel fa-3x text-muted"></i>
                                </div>
                            {% endif %}
                            
                            <div class="card-body">
                                <h5 class="card-title">{{ hotel.name }}</h5>
                                
                                <div class="star-rating mb-2">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= hotel.star_rating %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star empty"></i>
                                        {% endif %}
                                    {% endfor %}
                                    <span class="ms-1">({{ hotel.star_rating }})</span>
                                </div>
                                
                                <div class="price-comparison">
                                    <h6><i class="fas fa-dollar-sign me-1"></i>Price Comparison</h6>

                                    {% if hotel.has_comparison %}
                                        <!-- Both platforms available -->
                                        {% if hotel.best_platform == 'booking' %}
                                            <span class="best-price">
                                                <i class="fas fa-bed me-1"></i>Booking.com: ${{ hotel.booking_price }}
                                            </span><br>
                                            <span class="platform-price">
                                                <i class="fas fa-suitcase me-1"></i>Agoda: ${{ hotel.agoda_price }}
                                            </span>
                                            {% if hotel.savings > 0 %}
                                                <div class="mt-1">
                                                    <small class="text-success">
                                                        <i class="fas fa-piggy-bank me-1"></i>Save ${{ hotel.savings|floatformat:0 }}
                                                    </small>
                                                </div>
                                            {% endif %}
                                        {% else %}
                                            <span class="platform-price">
                                                <i class="fas fa-bed me-1"></i>Booking.com: ${{ hotel.booking_price }}
                                            </span><br>
                                            <span class="best-price">
                                                <i class="fas fa-suitcase me-1"></i>Agoda: ${{ hotel.agoda_price }}
                                            </span>
                                            {% if hotel.savings > 0 %}
                                                <div class="mt-1">
                                                    <small class="text-success">
                                                        <i class="fas fa-piggy-bank me-1"></i>Save ${{ hotel.savings|floatformat:0 }}
                                                    </small>
                                                </div>
                                            {% endif %}
                                        {% endif %}
                                    {% else %}
                                        <!-- Single platform -->
                                        {% if hotel.best_platform == 'booking' %}
                                            <span class="best-price">
                                                <i class="fas fa-bed me-1"></i>Booking.com: ${{ hotel.best_price }}
                                            </span>
                                        {% else %}
                                            <span class="best-price">
                                                <i class="fas fa-suitcase me-1"></i>Agoda: ${{ hotel.best_price }}
                                            </span>
                                        {% endif %}
                                    {% endif %}
                                </div>
                                
                                <div class="mt-3">
                                    <a href="{{ hotel.url }}" target="_blank" class="btn btn-primary btn-sm me-2">
                                        <i class="fas fa-external-link-alt me-1"></i>Book Now
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% elif request.method == 'POST' %}
            <div class="no-results">
                <i class="fas fa-search fa-3x mb-3 text-muted"></i>
                <h4>No hotels found</h4>
                <p>Try adjusting your search criteria</p>
            </div>
        {% endif %}
    </div>

    <!-- Popular Destinations -->
    {% if not hotels and request.method != 'POST' %}
        <div class="row mt-5">
            <div class="col-12">
                <h3 class="text-center mb-4">Popular Destinations</h3>
                <div class="row">
                    <div class="col-md-3 col-sm-6 mb-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-city fa-2x text-primary mb-2"></i>
                                <h6>New York</h6>
                                <button class="btn btn-outline-primary btn-sm" onclick="searchCity('New York')">
                                    Search Hotels
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6 mb-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-landmark fa-2x text-primary mb-2"></i>
                                <h6>Paris</h6>
                                <button class="btn btn-outline-primary btn-sm" onclick="searchCity('Paris')">
                                    Search Hotels
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6 mb-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-torii-gate fa-2x text-primary mb-2"></i>
                                <h6>Tokyo</h6>
                                <button class="btn btn-outline-primary btn-sm" onclick="searchCity('Tokyo')">
                                    Search Hotels
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6 mb-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-bridge fa-2x text-primary mb-2"></i>
                                <h6>London</h6>
                                <button class="btn btn-outline-primary btn-sm" onclick="searchCity('London')">
                                    Search Hotels
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
function searchCity(cityName) {
    $('#city').val(cityName);
    $('#search-form').submit();
}

$(document).ready(function() {
    {% if hotels %}
        $('#search-results').show();
    {% endif %}
    
    // Initialize bookmark buttons
    $('.bookmark-btn').on('click', function(e) {
        e.preventDefault();
        toggleBookmark($(this));
    });
});
</script>
{% endblock %}
