{% extends 'base.html' %}

{% block title %}Hotel Search{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Simple Search Form -->
    <div class="row justify-content-center mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">🏨 Hotel Search</h3>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="city" class="form-label">City</label>
                                <input type="text" class="form-control" id="city" name="city" 
                                       placeholder="Paris, Tokyo, London..." value="{{ city|default:'' }}" required>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="min_price" class="form-label">Min Price</label>
                                <input type="number" class="form-control" id="min_price" name="min_price" placeholder="50">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="max_price" class="form-label">Max Price</label>
                                <input type="number" class="form-control" id="max_price" name="max_price" placeholder="500">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="star_rating" class="form-label">Min Stars</label>
                                <select class="form-select" id="star_rating" name="star_rating">
                                    <option value="">Any</option>
                                    <option value="3">3+</option>
                                    <option value="4">4+</option>
                                    <option value="5">5</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">
                                    🔍 Search Hotels
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Section -->
    {% if hotels %}
        <div class="alert alert-info text-center">
            <strong>✅ Found {{ hotels|length }} hotels in {{ city }}</strong>
        </div>
        
        <div class="row">
            {% for hotel in hotels %}
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100">
                        <!-- Hotel Image -->
                        {% if hotel.image_url %}
                            <img src="{{ hotel.image_url }}" class="card-img-top" alt="{{ hotel.name }}" style="height: 200px; object-fit: cover;">
                        {% else %}
                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                🏨
                            </div>
                        {% endif %}
                        
                        <div class="card-body">
                            <!-- Hotel Name -->
                            <h5 class="card-title">{{ hotel.name }}</h5>
                            
                            <!-- Star Rating -->
                            <div class="mb-2">
                                {% for i in "12345" %}
                                    {% if forloop.counter <= hotel.star_rating %}
                                        ⭐
                                    {% else %}
                                        ☆
                                    {% endif %}
                                {% endfor %}
                                <span class="ms-1">({{ hotel.star_rating }} stars)</span>
                            </div>

                            <!-- Price Information - Always Visible -->
                            <div class="price-info mb-3">
                                <!-- Best Price Highlight - Always Show -->
                                <div class="alert alert-success mb-2">
                                    <strong>🏆 Best Price: ${{ hotel.best_price|floatformat:0 }}</strong>
                                    {% if hotel.best_platform %}
                                        <br><small>via {{ hotel.best_platform|title }}</small>
                                    {% endif %}
                                    {% if hotel.has_comparison and hotel.savings > 0 %}
                                        <br><small class="text-success">💰 Save ${{ hotel.savings|floatformat:0 }}!</small>
                                    {% endif %}
                                </div>

                                <!-- Platform Prices - Always Show When Available -->
                                {% if hotel.booking_price or hotel.agoda_price %}
                                    <div class="row mb-2">
                                        {% if hotel.booking_price %}
                                            <div class="col-{% if hotel.agoda_price %}6{% else %}12{% endif %}">
                                                <div class="text-center p-2 border rounded {% if hotel.best_platform == 'booking' %}bg-success text-white{% else %}bg-light{% endif %}">
                                                    <small>📘 Booking.com</small>
                                                    <div><strong>${{ hotel.booking_price }}</strong></div>
                                                    {% if hotel.best_platform == 'booking' and hotel.has_comparison %}
                                                        <small class="text-success">✨ Best Deal</small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        {% endif %}
                                        {% if hotel.agoda_price %}
                                            <div class="col-{% if hotel.booking_price %}6{% else %}12{% endif %}">
                                                <div class="text-center p-2 border rounded {% if hotel.best_platform == 'agoda' %}bg-success text-white{% else %}bg-light{% endif %}">
                                                    <small>🅰️ Agoda</small>
                                                    <div><strong>${{ hotel.agoda_price }}</strong></div>
                                                    {% if hotel.best_platform == 'agoda' and hotel.has_comparison %}
                                                        <small class="text-success">✨ Best Deal</small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        {% endif %}
                                    </div>

                                    <!-- Comparison Info -->
                                    {% if hotel.has_comparison %}
                                        <div class="text-center">
                                            <small class="text-muted">
                                                💡 Comparing {{ hotel.booking_price|yesno:"Booking.com," }}{% if hotel.booking_price and hotel.agoda_price %} and {% endif %}{{ hotel.agoda_price|yesno:"Agoda," }}
                                            </small>
                                        </div>
                                    {% else %}
                                        <div class="text-center">
                                            <small class="text-muted">
                                                📍 Available on {{ hotel.best_platform|title }} only
                                            </small>
                                        </div>
                                    {% endif %}
                                {% endif %}
                            </div>

                            <!-- Buttons -->
                            <div class="d-flex gap-2">
                                {% if hotel.url %}
                                    <a href="{{ hotel.url }}" target="_blank" class="btn btn-primary btn-sm flex-fill">
                                        📖 Book Now
                                    </a>
                                {% endif %}

                                {% if hotel.booking_hotel or hotel.agoda_hotel %}
                                    <button type="button" class="btn btn-outline-warning btn-sm"
                                            onclick="addBookmark({% if hotel.booking_hotel %}{{ hotel.booking_hotel.id }}{% elif hotel.agoda_hotel %}{{ hotel.agoda_hotel.id }}{% endif %}); return false;">
                                        🔖
                                    </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% elif city %}
        <div class="alert alert-warning text-center">
            <h4>❌ No Hotels Found</h4>
            <p>No hotels found for "{{ city }}". Try: Paris, Tokyo, London, or New York.</p>
        </div>
    {% else %}
        <!-- Quick Search Buttons -->
        <div class="text-center">
            <h4 class="mb-4">🌍 Popular Destinations</h4>
            <div class="row">
                <div class="col-md-3 mb-3">
                    <form method="post" class="d-inline">
                        {% csrf_token %}
                        <input type="hidden" name="city" value="Paris">
                        <button type="submit" class="btn btn-outline-primary btn-lg w-100">🗼 Paris</button>
                    </form>
                </div>
                <div class="col-md-3 mb-3">
                    <form method="post" class="d-inline">
                        {% csrf_token %}
                        <input type="hidden" name="city" value="Tokyo">
                        <button type="submit" class="btn btn-outline-primary btn-lg w-100">🏯 Tokyo</button>
                    </form>
                </div>
                <div class="col-md-3 mb-3">
                    <form method="post" class="d-inline">
                        {% csrf_token %}
                        <input type="hidden" name="city" value="London">
                        <button type="submit" class="btn btn-outline-primary btn-lg w-100">👑 London</button>
                    </form>
                </div>
                <div class="col-md-3 mb-3">
                    <form method="post" class="d-inline">
                        {% csrf_token %}
                        <input type="hidden" name="city" value="New York">
                        <button type="submit" class="btn btn-outline-primary btn-lg w-100">🗽 New York</button>
                    </form>
                </div>
            </div>
        </div>
    {% endif %}
</div>

<script>
function addBookmark(hotelId) {
    try {
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

        if (!csrfToken) {
            alert('❌ Security token not found. Please refresh the page.');
            return false;
        }

        fetch('{% url "bookmarks:add" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': csrfToken,
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            },
            body: 'hotel_id=' + hotelId
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                alert('✅ ' + (data.message || 'Hotel bookmarked successfully!'));
            } else {
                alert('⚠️ ' + (data.error || 'Failed to bookmark hotel'));
            }
        })
        .catch(error => {
            console.error('Bookmark error:', error);
            alert('❌ Error bookmarking hotel. Please try again.');
        });

    } catch (error) {
        console.error('Bookmark function error:', error);
        alert('❌ Error bookmarking hotel. Please try again.');
    }

    return false;
}
</script>
{% endblock %}
