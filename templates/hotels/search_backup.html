{% extends 'base.html' %}
{% load static %}

{% block title %}Hotel Search{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1 class="text-center mb-4">Hotel Search</h1>
    
    <!-- Search Form -->
    <div class="row justify-content-center">
        <div class="col-md-8">
            <form method="post" action="{% url 'hotels:search' %}" class="card p-4">
                {% csrf_token %}
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="city" class="form-label">City</label>
                        <input type="text" class="form-control" id="city" name="city" 
                               value="{{ city }}" placeholder="Enter city name" required>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="min_price" class="form-label">Min Price</label>
                        <input type="number" class="form-control" id="min_price" name="min_price" 
                               placeholder="$0">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="max_price" class="form-label">Max Price</label>
                        <input type="number" class="form-control" id="max_price" name="max_price" 
                               placeholder="$1000">
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="star_rating" class="form-label">Minimum Star Rating</label>
                        <select class="form-control" id="star_rating" name="star_rating">
                            <option value="">Any Rating</option>
                            <option value="1">1+ Stars</option>
                            <option value="2">2+ Stars</option>
                            <option value="3">3+ Stars</option>
                            <option value="4">4+ Stars</option>
                            <option value="5">5 Stars</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search"></i> Search Hotels
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Search Results -->
    {% if request.method == 'POST' %}
        <div class="mt-4">
            <div class="alert alert-info">
                <strong>Search Results for "{{ city }}"</strong> - Found {{ hotels|length }} hotels
            </div>
            
            {% if hotels %}
                <div class="row">
                    {% for hotel in hotels %}
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">{{ hotel.name }}</h5>
                                    <div class="mb-2">
                                        {% for i in "12345" %}
                                            {% if forloop.counter <= hotel.star_rating %}
                                                <i class="fas fa-star text-warning"></i>
                                            {% else %}
                                                <i class="far fa-star text-muted"></i>
                                            {% endif %}
                                        {% endfor %}
                                        <span class="ms-1">({{ hotel.star_rating }})</span>
                                    </div>
                                    
                                    <div class="price-info">
                                        <h6 class="text-primary">Best Price: ${{ hotel.best_price }}</h6>
                                        <small class="text-muted">via {{ hotel.best_platform|title }}</small>
                                        
                                        {% if hotel.has_comparison %}
                                            <div class="mt-2">
                                                <small class="text-success">
                                                    <i class="fas fa-check-circle"></i> Price comparison available
                                                </small>
                                            </div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="mt-3">
                                        <a href="{{ hotel.url }}" target="_blank" class="btn btn-primary btn-sm">
                                            <i class="fas fa-external-link-alt"></i> Book Now
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="alert alert-warning text-center">
                    <i class="fas fa-search fa-2x mb-3"></i>
                    <h4>No hotels found</h4>
                    <p>Try adjusting your search criteria or search for a different city.</p>
                </div>
            {% endif %}
        </div>
    {% endif %}
</div>
{% endblock %}
